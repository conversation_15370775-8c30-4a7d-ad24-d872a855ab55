/**
 * Floating Panel Styles
 * Minimalist, draggable interface
 */

body {
    margin: 0;
    padding: 0;
    background: transparent;
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Main Floating Panel */
.floating-panel {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 280px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    z-index: 10000;
    user-select: none;
    transition: all 0.3s ease;
}

.floating-panel:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Dark theme support */
.theme-dark .floating-panel {
    background: rgba(45, 45, 45, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
    color: white;
}

/* Panel Header */
.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    cursor: move;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 12px 12px 0 0;
}

.panel-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 14px;
}

.title-icon {
    font-size: 16px;
}

.panel-controls {
    display: flex;
    gap: 4px;
}

.panel-btn {
    width: 24px;
    height: 24px;
    border: none;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.2s ease;
}

.panel-btn:hover {
    background: rgba(0, 0, 0, 0.2);
    transform: scale(1.1);
}

/* Panel Content */
.panel-content {
    padding: 16px;
}

/* Status Section */
.status-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.status-indicator-large {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #95a5a6;
    transition: all 0.3s ease;
}

.status-recording .status-dot {
    background: #e74c3c;
    animation: pulse 1.5s infinite;
}

.status-paused .status-dot {
    background: #f39c12;
}

.status-text {
    font-size: 14px;
    font-weight: 500;
}

.recording-timer {
    font-family: 'Courier New', monospace;
    font-size: 16px;
    font-weight: bold;
    color: #3498db;
    background: rgba(52, 152, 219, 0.1);
    padding: 4px 8px;
    border-radius: 6px;
    min-width: 50px;
    text-align: center;
}

/* Quick Controls */
.quick-controls {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 16px;
}

.control-btn {
    width: 48px;
    height: 48px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    transition: all 0.2s ease;
    position: relative;
}

.record-btn {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.record-btn:hover:not(:disabled) {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(231, 76, 60, 0.4);
}

.pause-btn {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
}

.pause-btn:hover:not(:disabled) {
    transform: scale(1.1);
}

.stop-btn {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
    box-shadow: 0 4px 12px rgba(149, 165, 166, 0.3);
}

.stop-btn:hover:not(:disabled) {
    transform: scale(1.1);
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Quick Stats */
.quick-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 16px;
    padding: 12px;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 18px;
    font-weight: bold;
    color: #3498db;
}

.stat-label {
    font-size: 11px;
    color: #7f8c8d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Quick Actions */
.quick-actions {
    display: flex;
    justify-content: space-around;
}

.action-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* Minimized Panel */
.minimized-panel {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.minimized-panel:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.minimized-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.minimized-status {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.restore-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
}

/* Modal Overlays */
.modal-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 20000;
    align-items: center;
    justify-content: center;
}

.modal-overlay.show {
    display: flex;
}

/* Settings Modal */
.settings-modal,
.history-modal,
.help-modal {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 90vw;
    max-height: 80vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: #f5f5f5;
    color: #333;
}

.modal-content {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 16px 24px 24px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    border-top: 1px solid #eee;
}

/* Settings Specific */
.setting-group {
    margin-bottom: 20px;
}

.setting-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 14px;
}

.setting-slider {
    width: 100%;
    margin-bottom: 4px;
}

.setting-value {
    font-size: 12px;
    color: #666;
    float: right;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

/* Help Specific */
.help-section {
    margin-bottom: 24px;
}

.help-section h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    color: #333;
}

.shortcut-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.shortcut-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

kbd {
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    font-family: monospace;
}

.tip-list {
    margin: 0;
    padding-left: 20px;
}

.tip-list li {
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 1.5;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 30000;
    pointer-events: none;
}

.toast {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    margin-bottom: 8px;
    font-size: 14px;
    animation: toastSlideIn 0.3s ease;
    pointer-events: auto;
}

@keyframes toastSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .floating-panel {
        width: calc(100vw - 40px);
        left: 20px;
        right: 20px;
    }
    
    .settings-modal,
    .history-modal,
    .help-modal {
        width: calc(100vw - 40px);
        margin: 20px;
    }
}

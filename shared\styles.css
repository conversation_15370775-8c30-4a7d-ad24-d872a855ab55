/**
 * Shared CSS Styles
 * Common styling used across all UI variants
 */

/* CSS Variables for theming */
:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --error-color: #e74c3c;
    --background-color: #ffffff;
    --surface-color: #f8f9fa;
    --text-color: #2c3e50;
    --text-muted: #7f8c8d;
    --border-color: #dee2e6;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --border-radius: 6px;
    --transition: all 0.3s ease;
}

/* Dark theme */
.theme-dark {
    --background-color: #1a1a1a;
    --surface-color: #2d2d2d;
    --text-color: #ffffff;
    --text-muted: #b0b0b0;
    --border-color: #404040;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* High contrast theme */
.theme-high-contrast {
    --primary-color: #0066cc;
    --background-color: #ffffff;
    --surface-color: #ffffff;
    --text-color: #000000;
    --border-color: #000000;
}

/* Base styles */
* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    transition: var(--transition);
}

/* Button styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    gap: 8px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: #2980b9;
    transform: translateY(-1px);
}

.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-success:hover:not(:disabled) {
    background-color: #27ae60;
}

.btn-warning {
    background-color: var(--warning-color);
    color: white;
}

.btn-warning:hover:not(:disabled) {
    background-color: #e67e22;
}

.btn-error {
    background-color: var(--error-color);
    color: white;
}

.btn-error:hover:not(:disabled) {
    background-color: #c0392b;
}

.btn-secondary {
    background-color: var(--surface-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--border-color);
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

.btn-lg {
    padding: 12px 24px;
    font-size: 16px;
}

/* Card styles */
.card {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.card-header {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
}

.card-body {
    padding: 16px;
}

.card-footer {
    padding: 16px;
    border-top: 1px solid var(--border-color);
    background-color: var(--background-color);
}

/* Form styles */
.form-group {
    margin-bottom: 16px;
}

.form-label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    color: var(--text-color);
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--background-color);
    color: var(--text-color);
    font-size: 14px;
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-control:disabled {
    background-color: var(--surface-color);
    opacity: 0.6;
}

/* Status indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-recording {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--error-color);
}

.status-recording::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--error-color);
    animation: pulse 1s infinite;
}

.status-idle {
    background-color: rgba(127, 140, 141, 0.1);
    color: var(--text-muted);
}

.status-processing {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--primary-color);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Progress bar */
.progress {
    width: 100%;
    height: 8px;
    background-color: var(--surface-color);
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

/* Metrics display */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.metric-card {
    text-align: center;
    padding: 16px;
}

.metric-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
}

.metric-label {
    font-size: 12px;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Data table */
.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 16px;
}

.data-table th,
.data-table td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background-color: var(--surface-color);
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.data-table tr:hover {
    background-color: var(--surface-color);
}

/* Utility classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-muted { color: var(--text-muted); }
.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }

.d-flex { display: flex; }
.d-block { display: block; }
.d-none { display: none; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }

.gap-1 { gap: 8px; }
.gap-2 { gap: 16px; }
.gap-3 { gap: 24px; }

/* Responsive design */
@media (max-width: 768px) {
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
    
    .card-body {
        padding: 12px;
    }
}

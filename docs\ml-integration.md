# ML Integration Guide

## Data Schema

### Session Metadata
```json
{
  "session": {
    "id": "uuid-v4",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "duration": 30000,
    "url": "https://example.com",
    "userAgent": "Mozilla/5.0...",
    "viewport": {"width": 1920, "height": 1080},
    "screen": {"width": 1920, "height": 1080, "pixelRatio": 1}
  }
}
```

### Interaction Types

#### Mouse Movement
```json
{
  "type": "mouse_move",
  "timestamp": 1500,
  "x": 100,
  "y": 200,
  "screenX": 100,
  "screenY": 200,
  "velocity": {"x": 5.2, "y": -2.1},
  "target": {
    "tagName": "DIV",
    "id": "content",
    "className": "main-content",
    "textContent": "Sample text...",
    "attributes": {"data-id": "123"}
  }
}
```

#### Mouse Click
```json
{
  "type": "mouse_click",
  "timestamp": 2000,
  "x": 100,
  "y": 200,
  "button": 0,
  "detail": 1,
  "target": {...}
}
```

#### Scroll Events
```json
{
  "type": "scroll",
  "timestamp": 2500,
  "scrollX": 0,
  "scrollY": 300,
  "deltaX": 0,
  "deltaY": 100,
  "deltaMode": 0,
  "target": {...}
}
```

#### Keyboard Events
```json
{
  "type": "key_down",
  "timestamp": 3000,
  "key": "a",
  "code": "KeyA",
  "keyCode": 65,
  "ctrlKey": false,
  "shiftKey": false,
  "altKey": false,
  "metaKey": false,
  "target": {...}
}
```

## Training Data Quality Metrics

### Coverage Metrics
- **Interaction Density**: Interactions per second
- **Spatial Coverage**: Screen area coverage percentage
- **Element Diversity**: Unique element types interacted with
- **Temporal Patterns**: Rhythm and timing consistency

### Behavioral Patterns
- **Mouse Movement Smoothness**: Velocity and acceleration curves
- **Scroll Momentum**: Natural deceleration patterns
- **Typing Rhythm**: Inter-keystroke intervals
- **Pause Patterns**: Natural hesitation points

## Nodriver Integration

### Data Preprocessing
```python
def preprocess_for_nodriver(session_data):
    """Convert recorded data to nodriver-compatible format"""
    actions = []
    
    for interaction in session_data['interactions']:
        if interaction['type'] == 'mouse_move':
            actions.append({
                'action': 'move_mouse',
                'x': interaction['x'],
                'y': interaction['y'],
                'duration': calculate_duration(interaction)
            })
        elif interaction['type'] == 'mouse_click':
            actions.append({
                'action': 'click',
                'x': interaction['x'],
                'y': interaction['y'],
                'button': interaction['button']
            })
    
    return actions
```

### Training Pipeline Integration
```python
class HumanBehaviorModel:
    def __init__(self):
        self.movement_model = MouseMovementPredictor()
        self.timing_model = InteractionTimingPredictor()
        self.element_selector = ElementSelectionPredictor()
    
    def train(self, session_data_list):
        """Train on collected human interaction data"""
        for session in session_data_list:
            # Extract features for each model
            movement_features = self.extract_movement_features(session)
            timing_features = self.extract_timing_features(session)
            selection_features = self.extract_selection_features(session)
            
            # Train individual models
            self.movement_model.fit(movement_features)
            self.timing_model.fit(timing_features)
            self.element_selector.fit(selection_features)
    
    def generate_human_like_action(self, target_element):
        """Generate human-like interaction for nodriver"""
        # Predict mouse path
        path = self.movement_model.predict_path(target_element)
        
        # Predict timing
        timing = self.timing_model.predict_timing(target_element)
        
        # Generate nodriver actions
        return self.convert_to_nodriver_actions(path, timing)
```

## Quality Validation

### Data Quality Checks
1. **Completeness**: All required fields present
2. **Consistency**: Logical sequence of events
3. **Realism**: Human-like patterns and timing
4. **Diversity**: Varied interaction patterns

### Validation Functions
```javascript
function validateSessionQuality(sessionData) {
    const metrics = {
        interactionDensity: calculateInteractionDensity(sessionData),
        spatialCoverage: calculateSpatialCoverage(sessionData),
        temporalConsistency: checkTemporalConsistency(sessionData),
        elementDiversity: calculateElementDiversity(sessionData)
    };
    
    return {
        score: calculateQualityScore(metrics),
        metrics: metrics,
        recommendations: generateRecommendations(metrics)
    };
}
```

## Export Formats

### JSON (Default)
Complete data with all metadata and interactions.

### CSV (Simplified)
Flattened format for quick analysis:
```csv
timestamp,type,x,y,key,target_tag,target_id,velocity_x,velocity_y
1500,mouse_move,100,200,,,DIV,content,5.2,-2.1
2000,mouse_click,100,200,,,DIV,content,0,0
```

### Parquet (Big Data)
Optimized for large-scale ML training pipelines.

## Best Practices

### Data Collection
1. **Diverse Scenarios**: Collect data across different websites and tasks
2. **Natural Behavior**: Encourage users to interact naturally
3. **Quality Control**: Implement real-time quality monitoring
4. **Privacy**: Sanitize sensitive information

### Model Training
1. **Feature Engineering**: Extract meaningful behavioral features
2. **Temporal Modeling**: Preserve timing relationships
3. **Regularization**: Prevent overfitting to specific users
4. **Validation**: Test on held-out interaction scenarios

### Production Deployment
1. **A/B Testing**: Compare AI vs human-like interactions
2. **Monitoring**: Track detection rates and success metrics
3. **Adaptation**: Continuously update models with new data
4. **Fallbacks**: Implement graceful degradation strategies

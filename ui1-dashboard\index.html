<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Control Center - Human Interaction Recorder</title>
    <link rel="stylesheet" href="../shared/styles.css">
    <link rel="stylesheet" href="dashboard.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-content">
                <h1 class="dashboard-title">
                    <span class="title-icon">📊</span>
                    Human Interaction Recorder
                </h1>
                <div class="header-controls">
                    <div class="status-display">
                        <span class="status-indicator status-idle" id="recordingStatus">
                            Idle
                        </span>
                    </div>
                    <select class="form-control theme-selector" id="themeSelector">
                        <option value="default">Light Theme</option>
                        <option value="dark">Dark Theme</option>
                        <option value="high-contrast">High Contrast</option>
                    </select>
                </div>
            </div>
        </header>

        <!-- Main Dashboard -->
        <main class="dashboard-main">
            <!-- Control Panel -->
            <section class="control-panel">
                <div class="card">
                    <div class="card-header">
                        <h2>Recording Controls</h2>
                    </div>
                    <div class="card-body">
                        <div class="control-buttons">
                            <button class="btn btn-success btn-lg" id="startRecording">
                                <span class="btn-icon">🔴</span>
                                Start Recording
                            </button>
                            <button class="btn btn-warning btn-lg" id="pauseRecording" disabled>
                                <span class="btn-icon">⏸️</span>
                                Pause
                            </button>
                            <button class="btn btn-error btn-lg" id="stopRecording" disabled>
                                <span class="btn-icon">⏹️</span>
                                Stop Recording
                            </button>
                        </div>
                        
                        <div class="recording-settings">
                            <div class="form-group">
                                <label class="form-label">Target Website URL</label>
                                <input type="url" class="form-control" id="targetUrl" 
                                       placeholder="https://example.com" value="">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Session Description</label>
                                <textarea class="form-control" id="sessionDescription" 
                                         placeholder="Describe the task or interaction scenario..."
                                         rows="3"></textarea>
                            </div>
                            
                            <div class="settings-grid">
                                <div class="form-group">
                                    <label class="form-label">Sample Rate (Hz)</label>
                                    <input type="number" class="form-control" id="sampleRate" 
                                           value="60" min="10" max="120">
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">
                                        <input type="checkbox" id="captureVelocity" checked>
                                        Capture Velocity
                                    </label>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">
                                        <input type="checkbox" id="captureAcceleration" checked>
                                        Capture Acceleration
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Real-time Metrics -->
            <section class="metrics-section">
                <div class="card">
                    <div class="card-header">
                        <h2>Real-time Metrics</h2>
                        <div class="recording-timer" id="recordingTimer">00:00:00</div>
                    </div>
                    <div class="card-body">
                        <div class="metrics-grid">
                            <div class="metric-card">
                                <div class="metric-value" id="totalInteractions">0</div>
                                <div class="metric-label">Total Interactions</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" id="mouseMovements">0</div>
                                <div class="metric-label">Mouse Movements</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" id="mouseClicks">0</div>
                                <div class="metric-label">Mouse Clicks</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" id="keystrokes">0</div>
                                <div class="metric-label">Keystrokes</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" id="scrollEvents">0</div>
                                <div class="metric-label">Scroll Events</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" id="dataSize">0 KB</div>
                                <div class="metric-label">Data Size</div>
                            </div>
                        </div>
                        
                        <div class="interaction-rate-chart">
                            <canvas id="interactionChart" width="400" height="100"></canvas>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Session Management -->
            <section class="session-management">
                <div class="card">
                    <div class="card-header">
                        <h2>Session Management</h2>
                        <div class="header-actions">
                            <button class="btn btn-secondary btn-sm" id="refreshSessions">
                                <span class="btn-icon">🔄</span>
                                Refresh
                            </button>
                            <button class="btn btn-primary btn-sm" id="exportAll">
                                <span class="btn-icon">📤</span>
                                Export All
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="session-list" id="sessionList">
                            <div class="empty-state">
                                <p>No recorded sessions yet. Start recording to see sessions here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Data Visualization -->
            <section class="visualization-section">
                <div class="card">
                    <div class="card-header">
                        <h2>Live Data Visualization</h2>
                        <div class="visualization-controls">
                            <select class="form-control" id="visualizationType">
                                <option value="heatmap">Mouse Heatmap</option>
                                <option value="path">Mouse Path</option>
                                <option value="velocity">Velocity Graph</option>
                                <option value="timing">Timing Analysis</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="visualization-container">
                            <canvas id="visualizationCanvas" width="800" height="400"></canvas>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Export Modal -->
        <div class="modal" id="exportModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Export Session Data</h3>
                    <button class="modal-close" id="closeExportModal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label class="form-label">Export Format</label>
                        <select class="form-control" id="exportFormat">
                            <option value="json">JSON (Complete)</option>
                            <option value="csv">CSV (Simplified)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Include Metadata</label>
                        <input type="checkbox" id="includeMetadata" checked>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="cancelExport">Cancel</button>
                    <button class="btn btn-primary" id="confirmExport">Export</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../shared/data-collector.js"></script>
    <script src="../shared/utils.js"></script>
    <script src="dashboard.js"></script>
</body>
</html>

/**
 * Timeline-Based Recorder Controller
 * Professional timeline interface for recording and analyzing human interactions
 */

class TimelineRecorderController {
    constructor() {
        this.collector = null;
        this.isRecording = false;
        this.isPaused = false;
        this.isPlaying = false;
        this.currentSession = null;
        this.sessions = [];
        this.selectedEvent = null;
        
        // Timeline properties
        this.timelineScale = 1; // pixels per millisecond
        this.timelineOffset = 0; // scroll offset
        this.playheadPosition = 0; // current playback position
        this.sessionDuration = 0;
        
        // Track visibility
        this.trackVisibility = {
            mouse: true,
            click: true,
            keyboard: true,
            scroll: true
        };
        
        // Canvas contexts
        this.canvases = {};
        
        this.initializeUI();
        this.bindEvents();
        this.loadSessions();
        this.setupTimeline();
    }
    
    initializeUI() {
        // Initialize canvas elements
        this.canvases.mouse = document.getElementById('mouseCanvas');
        this.canvases.scroll = document.getElementById('scrollCanvas');
        
        // Set canvas sizes
        this.resizeCanvases();
        
        // Load saved sessions
        this.refreshSessionList();
    }
    
    bindEvents() {
        // Recording controls
        document.getElementById('recordBtn').addEventListener('click', () => this.toggleRecording());
        document.getElementById('pauseBtn').addEventListener('click', () => this.pauseRecording());
        document.getElementById('stopBtn').addEventListener('click', () => this.stopRecording());
        
        // Playback controls
        document.getElementById('playBtn').addEventListener('click', () => this.togglePlayback());
        document.getElementById('rewindBtn').addEventListener('click', () => this.rewindPlayback());
        
        // Session management
        document.getElementById('newSession').addEventListener('click', () => this.createNewSession());
        document.getElementById('refreshSessions').addEventListener('click', () => this.refreshSessionList());
        
        // Track controls
        this.bindTrackControls();
        
        // Timeline interaction
        this.bindTimelineEvents();
        
        // Export modal
        this.bindExportEvents();
        
        // Window resize
        window.addEventListener('resize', () => this.resizeCanvases());
    }
    
    bindTrackControls() {
        // Track toggle buttons
        document.querySelectorAll('.track-toggle').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const track = e.target.dataset.track;
                this.toggleTrackVisibility(track);
            });
        });
        
        // Track solo buttons
        document.querySelectorAll('.track-solo').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const track = e.target.dataset.track;
                this.soloTrack(track);
            });
        });
    }
    
    bindTimelineEvents() {
        const ruler = document.getElementById('timelineRuler');
        
        // Click to seek
        ruler.addEventListener('click', (e) => {
            const rect = ruler.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const time = (x + this.timelineOffset) / this.timelineScale;
            this.seekToTime(time);
        });
        
        // Scroll to zoom/pan
        ruler.addEventListener('wheel', (e) => {
            e.preventDefault();
            
            if (e.ctrlKey) {
                // Zoom
                const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
                this.zoomTimeline(zoomFactor, e.clientX);
            } else {
                // Pan
                this.timelineOffset += e.deltaX;
                this.updateTimeline();
            }
        });
    }
    
    bindExportEvents() {
        document.getElementById('closeExportModal').addEventListener('click', () => this.hideExportModal());
        document.getElementById('cancelExport').addEventListener('click', () => this.hideExportModal());
        document.getElementById('confirmExport').addEventListener('click', () => this.exportSession());
        
        // Click outside to close
        document.getElementById('exportModal').addEventListener('click', (e) => {
            if (e.target.id === 'exportModal') {
                this.hideExportModal();
            }
        });
    }
    
    setupTimeline() {
        this.updateRuler();
        this.updateTimeline();
    }
    
    resizeCanvases() {
        Object.values(this.canvases).forEach(canvas => {
            if (canvas) {
                const rect = canvas.parentElement.getBoundingClientRect();
                canvas.width = rect.width;
                canvas.height = rect.height;
            }
        });
        
        if (this.currentSession) {
            this.renderTracks();
        }
    }
    
    toggleRecording() {
        if (this.isRecording) {
            this.stopRecording();
        } else {
            this.startRecording();
        }
    }
    
    startRecording() {
        if (this.isRecording) return;
        
        const settings = {
            sampleRate: 60,
            captureVelocity: true,
            captureAcceleration: true,
            captureScrollMomentum: true,
            onStart: (sessionId) => this.onRecordingStart(sessionId),
            onStop: (sessionData) => this.onRecordingStop(sessionData),
            onDataPoint: (interaction) => this.onDataPoint(interaction),
            onError: (error) => this.onError(error)
        };
        
        this.collector = new HumanInteractionCollector(settings);
        
        if (this.collector.startRecording()) {
            this.isRecording = true;
            this.isPaused = false;
            this.updateRecordingUI();
            this.startRecordingTimer();
        }
    }
    
    pauseRecording() {
        if (!this.isRecording || this.isPaused) return;
        
        this.isPaused = true;
        this.updateRecordingUI();
    }
    
    stopRecording() {
        if (!this.isRecording) return;
        
        const sessionData = this.collector.stopRecording();
        this.isRecording = false;
        this.isPaused = false;
        
        this.stopRecordingTimer();
        this.updateRecordingUI();
        
        if (sessionData) {
            this.saveSession(sessionData);
            this.loadSession(sessionData);
            this.refreshSessionList();
        }
    }
    
    onRecordingStart(sessionId) {
        console.log('Recording started:', sessionId);
    }
    
    onRecordingStop(sessionData) {
        console.log('Recording stopped:', sessionData);
    }
    
    onDataPoint(interaction) {
        // Real-time visualization during recording
        if (this.isRecording && !this.isPaused) {
            this.updateLiveVisualization(interaction);
        }
    }
    
    onError(error) {
        console.error('Recording error:', error);
        this.showNotification('Recording error: ' + error.message, 'error');
    }
    
    updateRecordingUI() {
        const recordBtn = document.getElementById('recordBtn');
        const pauseBtn = document.getElementById('pauseBtn');
        const stopBtn = document.getElementById('stopBtn');
        const status = document.getElementById('recordingStatus');
        const statusText = status.querySelector('.status-text');
        
        if (this.isRecording) {
            recordBtn.style.display = 'none';
            pauseBtn.disabled = false;
            stopBtn.disabled = false;
            
            if (this.isPaused) {
                status.className = 'recording-status paused';
                statusText.textContent = 'Paused';
            } else {
                status.className = 'recording-status recording';
                statusText.textContent = 'Recording';
            }
        } else {
            recordBtn.style.display = 'flex';
            pauseBtn.disabled = true;
            stopBtn.disabled = true;
            status.className = 'recording-status';
            statusText.textContent = 'Ready';
        }
    }
    
    startRecordingTimer() {
        this.recordingStartTime = Date.now();
        this.recordingTimer = setInterval(() => {
            if (!this.isPaused) {
                const elapsed = Date.now() - this.recordingStartTime;
                document.getElementById('currentTime').textContent = this.formatTime(elapsed);
                
                if (this.collector) {
                    document.getElementById('totalInteractions').textContent = 
                        `${this.collector.interactions.length} interactions`;
                }
            }
        }, 100);
    }
    
    stopRecordingTimer() {
        if (this.recordingTimer) {
            clearInterval(this.recordingTimer);
            this.recordingTimer = null;
        }
    }
    
    formatTime(milliseconds) {
        const totalSeconds = Math.floor(milliseconds / 1000);
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;
        
        if (hours > 0) {
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        } else {
            return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }
    
    togglePlayback() {
        if (!this.currentSession) return;
        
        if (this.isPlaying) {
            this.pausePlayback();
        } else {
            this.startPlayback();
        }
    }
    
    startPlayback() {
        if (!this.currentSession || this.isPlaying) return;
        
        this.isPlaying = true;
        document.getElementById('playBtn').classList.add('active');
        
        // Start playback animation
        this.playbackStartTime = Date.now();
        this.playbackTimer = setInterval(() => {
            const elapsed = Date.now() - this.playbackStartTime;
            const newPosition = this.playheadPosition + elapsed;
            
            if (newPosition >= this.sessionDuration) {
                this.stopPlayback();
                return;
            }
            
            this.seekToTime(newPosition);
        }, 16); // ~60fps
    }
    
    pausePlayback() {
        this.isPlaying = false;
        document.getElementById('playBtn').classList.remove('active');
        
        if (this.playbackTimer) {
            clearInterval(this.playbackTimer);
            this.playbackTimer = null;
        }
    }
    
    stopPlayback() {
        this.pausePlayback();
        this.seekToTime(0);
    }
    
    rewindPlayback() {
        this.seekToTime(0);
    }
    
    seekToTime(time) {
        this.playheadPosition = Math.max(0, Math.min(time, this.sessionDuration));
        this.updatePlayhead();
        this.updateEventProperties();
        
        // Update time display
        document.getElementById('currentTime').textContent = this.formatTime(this.playheadPosition);
    }

    updatePlayhead() {
        const playhead = document.getElementById('playhead');
        const position = (this.playheadPosition * this.timelineScale) - this.timelineOffset;
        playhead.style.left = position + 'px';
    }

    zoomTimeline(factor, centerX = null) {
        const oldScale = this.timelineScale;
        this.timelineScale *= factor;

        // Constrain zoom levels
        this.timelineScale = Math.max(0.001, Math.min(10, this.timelineScale));

        // Adjust offset to zoom around center point
        if (centerX !== null) {
            const timeAtCenter = (centerX + this.timelineOffset) / oldScale;
            this.timelineOffset = (timeAtCenter * this.timelineScale) - centerX;
        }

        this.updateTimeline();
    }

    updateTimeline() {
        this.updateRuler();
        this.updatePlayhead();
        this.renderTracks();
    }

    updateRuler() {
        const ruler = document.getElementById('rulerMarkers');
        const rulerWidth = ruler.parentElement.offsetWidth;

        // Clear existing markers
        ruler.innerHTML = '';

        if (!this.sessionDuration) return;

        // Calculate marker intervals
        const pixelsPerSecond = this.timelineScale * 1000;
        let interval = 1000; // Start with 1 second

        if (pixelsPerSecond < 50) interval = 10000; // 10 seconds
        else if (pixelsPerSecond < 20) interval = 30000; // 30 seconds
        else if (pixelsPerSecond < 10) interval = 60000; // 1 minute

        // Create markers
        for (let time = 0; time <= this.sessionDuration; time += interval) {
            const x = (time * this.timelineScale) - this.timelineOffset;

            if (x >= -50 && x <= rulerWidth + 50) {
                const marker = document.createElement('div');
                marker.className = 'ruler-marker';
                marker.style.left = x + 'px';

                // Add label for major markers
                if (time % (interval * 5) === 0) {
                    marker.classList.add('major');

                    const label = document.createElement('div');
                    label.className = 'ruler-label';
                    label.style.left = (x + 4) + 'px';
                    label.textContent = this.formatTime(time);
                    ruler.appendChild(label);
                }

                ruler.appendChild(marker);
            }
        }
    }

    renderTracks() {
        if (!this.currentSession) return;

        this.renderMouseTrack();
        this.renderScrollTrack();
        this.renderEventMarkers();
    }

    renderMouseTrack() {
        const canvas = this.canvases.mouse;
        if (!canvas || !this.trackVisibility.mouse) return;

        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        const mouseEvents = this.currentSession.interactions.filter(i => i.type === 'mousemove');
        if (mouseEvents.length === 0) return;

        ctx.strokeStyle = '#3498db';
        ctx.lineWidth = 2;
        ctx.globalAlpha = 0.7;

        ctx.beginPath();

        mouseEvents.forEach((event, index) => {
            const x = (event.timestamp * this.timelineScale) - this.timelineOffset;
            const y = canvas.height - ((event.data.y / window.innerHeight) * canvas.height);

            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });

        ctx.stroke();

        // Draw velocity indicators
        ctx.globalAlpha = 0.5;
        mouseEvents.forEach(event => {
            if (event.data.velocity && event.data.velocity > 100) {
                const x = (event.timestamp * this.timelineScale) - this.timelineOffset;
                const y = canvas.height - ((event.data.y / window.innerHeight) * canvas.height);
                const intensity = Math.min(event.data.velocity / 1000, 1);

                ctx.fillStyle = `rgba(231, 76, 60, ${intensity})`;
                ctx.beginPath();
                ctx.arc(x, y, 3, 0, Math.PI * 2);
                ctx.fill();
            }
        });
    }

    renderScrollTrack() {
        const canvas = this.canvases.scroll;
        if (!canvas || !this.trackVisibility.scroll) return;

        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        const scrollEvents = this.currentSession.interactions.filter(i => i.type === 'wheel');
        if (scrollEvents.length === 0) return;

        ctx.strokeStyle = '#2ecc71';
        ctx.lineWidth = 1;
        ctx.globalAlpha = 0.8;

        const centerY = canvas.height / 2;

        scrollEvents.forEach(event => {
            const x = (event.timestamp * this.timelineScale) - this.timelineOffset;
            const deltaY = Math.max(-50, Math.min(50, event.data.deltaY));
            const y = centerY - (deltaY * 2);

            ctx.beginPath();
            ctx.moveTo(x, centerY);
            ctx.lineTo(x, y);
            ctx.stroke();
        });
    }

    renderEventMarkers() {
        // Clear existing markers
        document.getElementById('clickMarkers').innerHTML = '';
        document.getElementById('keyboardMarkers').innerHTML = '';

        if (!this.currentSession) return;

        // Render click events
        if (this.trackVisibility.click) {
            const clickEvents = this.currentSession.interactions.filter(i => i.type === 'click');
            this.renderMarkers('clickMarkers', clickEvents, 'click');
        }

        // Render keyboard events
        if (this.trackVisibility.keyboard) {
            const keyEvents = this.currentSession.interactions.filter(i => i.type === 'keydown');
            this.renderMarkers('keyboardMarkers', keyEvents, 'key');
        }
    }

    renderMarkers(containerId, events, markerClass) {
        const container = document.getElementById(containerId);

        events.forEach(event => {
            const x = (event.timestamp * this.timelineScale) - this.timelineOffset;

            if (x >= -10 && x <= container.parentElement.offsetWidth + 10) {
                const marker = document.createElement('div');
                marker.className = `event-marker ${markerClass}`;
                marker.style.left = x + 'px';
                marker.dataset.eventId = event.id;

                marker.addEventListener('click', () => this.selectEvent(event));

                container.appendChild(marker);
            }
        });
    }

    selectEvent(event) {
        // Remove previous selection
        document.querySelectorAll('.event-marker.selected').forEach(marker => {
            marker.classList.remove('selected');
        });

        // Select new event
        const marker = document.querySelector(`[data-event-id="${event.id}"]`);
        if (marker) {
            marker.classList.add('selected');
        }

        this.selectedEvent = event;
        this.seekToTime(event.timestamp);
        this.updateEventProperties();
    }

    updateEventProperties() {
        const container = document.getElementById('propertiesContent');

        if (!this.selectedEvent) {
            container.innerHTML = '<div class="no-selection"><p>Select an event on the timeline to view its properties.</p></div>';
            return;
        }

        const event = this.selectedEvent;
        const properties = [
            { label: 'Event Type', value: event.type },
            { label: 'Timestamp', value: this.formatTime(event.timestamp) },
            { label: 'X Position', value: event.data.x || 'N/A' },
            { label: 'Y Position', value: event.data.y || 'N/A' },
            { label: 'Button', value: event.data.button !== undefined ? event.data.button : 'N/A' },
            { label: 'Key', value: event.data.key || 'N/A' },
            { label: 'Velocity', value: event.data.velocity ? event.data.velocity.toFixed(2) + ' px/s' : 'N/A' },
            { label: 'Acceleration', value: event.data.acceleration ? event.data.acceleration.toFixed(2) + ' px/s²' : 'N/A' }
        ];

        container.innerHTML = properties.map(prop => `
            <div class="property-item">
                <div class="property-label">${prop.label}</div>
                <div class="property-value">${prop.value}</div>
            </div>
        `).join('');
    }

    toggleTrackVisibility(trackName) {
        this.trackVisibility[trackName] = !this.trackVisibility[trackName];

        const button = document.querySelector(`[data-track="${trackName}"].track-toggle`);
        if (this.trackVisibility[trackName]) {
            button.classList.add('active');
        } else {
            button.classList.remove('active');
        }

        this.renderTracks();
    }

    soloTrack(trackName) {
        const isSolo = document.querySelector(`[data-track="${trackName}"].track-solo`).classList.contains('active');

        // Clear all solo states
        document.querySelectorAll('.track-solo').forEach(btn => btn.classList.remove('active'));

        if (!isSolo) {
            // Solo this track
            document.querySelector(`[data-track="${trackName}"].track-solo`).classList.add('active');

            // Hide all other tracks
            Object.keys(this.trackVisibility).forEach(track => {
                this.trackVisibility[track] = track === trackName;
                const toggleBtn = document.querySelector(`[data-track="${track}"].track-toggle`);
                if (this.trackVisibility[track]) {
                    toggleBtn.classList.add('active');
                } else {
                    toggleBtn.classList.remove('active');
                }
            });
        } else {
            // Un-solo - show all tracks
            Object.keys(this.trackVisibility).forEach(track => {
                this.trackVisibility[track] = true;
                document.querySelector(`[data-track="${track}"].track-toggle`).classList.add('active');
            });
        }

        this.renderTracks();
    }

    createNewSession() {
        if (this.isRecording) {
            this.stopRecording();
        }

        this.currentSession = null;
        this.selectedEvent = null;
        this.playheadPosition = 0;
        this.sessionDuration = 0;

        this.updateTimeline();
        this.updateEventProperties();
        this.updateAnalysis();

        // Clear session selection
        document.querySelectorAll('.session-item').forEach(item => {
            item.classList.remove('active');
        });

        document.getElementById('currentTime').textContent = '00:00:00';
        document.getElementById('totalInteractions').textContent = '0 interactions';
    }

    loadSession(sessionData) {
        this.currentSession = sessionData;
        this.selectedEvent = null;
        this.playheadPosition = 0;

        // Calculate session duration
        if (sessionData.interactions.length > 0) {
            const lastEvent = sessionData.interactions[sessionData.interactions.length - 1];
            this.sessionDuration = lastEvent.timestamp;
        } else {
            this.sessionDuration = 0;
        }

        // Enable playback controls
        document.getElementById('playBtn').disabled = this.sessionDuration === 0;
        document.getElementById('rewindBtn').disabled = this.sessionDuration === 0;

        this.updateTimeline();
        this.updateEventProperties();
        this.updateAnalysis();

        document.getElementById('totalInteractions').textContent = `${sessionData.interactions.length} interactions`;
    }

    saveSession(sessionData) {
        UIUtils.saveSession(sessionData);
        this.sessions.push({
            id: sessionData.session.id,
            timestamp: sessionData.session.startTime,
            duration: sessionData.session.duration,
            interactionCount: sessionData.interactions.length
        });
    }

    refreshSessionList() {
        const sessions = UIUtils.getSavedSessions();
        this.sessions = sessions;

        const container = document.getElementById('sessionList');

        if (sessions.length === 0) {
            container.innerHTML = '<div class="empty-state"><p>No sessions recorded yet. Click "New Session" to start.</p></div>';
            return;
        }

        container.innerHTML = sessions.map(session => `
            <div class="session-item" data-session-id="${session.id}">
                <div class="session-title">Session ${session.id.substring(0, 8)}</div>
                <div class="session-meta">
                    ${UIUtils.formatTimestamp(session.timestamp)} •
                    ${UIUtils.formatDuration(session.duration)} •
                    ${session.interactionCount} interactions
                </div>
            </div>
        `).join('');

        // Bind click events
        container.querySelectorAll('.session-item').forEach(item => {
            item.addEventListener('click', () => {
                const sessionId = item.dataset.sessionId;
                this.loadSessionById(sessionId);

                // Update selection
                container.querySelectorAll('.session-item').forEach(i => i.classList.remove('active'));
                item.classList.add('active');
            });
        });
    }

    loadSessionById(sessionId) {
        const sessionData = UIUtils.loadSession(sessionId);
        if (sessionData) {
            this.loadSession(sessionData);
        }
    }

    updateAnalysis() {
        if (!this.currentSession) {
            document.getElementById('sessionDuration').textContent = '--';
            document.getElementById('mouseDistance').textContent = '--';
            document.getElementById('avgSpeed').textContent = '--';
            document.getElementById('clickRate').textContent = '--';
            return;
        }

        const interactions = this.currentSession.interactions;
        const duration = this.sessionDuration / 1000; // Convert to seconds

        // Calculate mouse distance
        const mouseEvents = interactions.filter(i => i.type === 'mousemove');
        let totalDistance = 0;
        let totalVelocity = 0;
        let velocityCount = 0;

        for (let i = 1; i < mouseEvents.length; i++) {
            const prev = mouseEvents[i - 1];
            const curr = mouseEvents[i];

            const dx = curr.data.x - prev.data.x;
            const dy = curr.data.y - prev.data.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            totalDistance += distance;

            if (curr.data.velocity) {
                totalVelocity += curr.data.velocity;
                velocityCount++;
            }
        }

        // Calculate click rate
        const clickEvents = interactions.filter(i => i.type === 'click');
        const clickRate = duration > 0 ? (clickEvents.length / duration * 60) : 0; // clicks per minute

        // Update display
        document.getElementById('sessionDuration').textContent = this.formatTime(this.sessionDuration);
        document.getElementById('mouseDistance').textContent = Math.round(totalDistance) + ' px';
        document.getElementById('avgSpeed').textContent = velocityCount > 0 ?
            Math.round(totalVelocity / velocityCount) + ' px/s' : '--';
        document.getElementById('clickRate').textContent = clickRate.toFixed(1) + '/min';
    }

    updateLiveVisualization(interaction) {
        // Update live metrics during recording
        if (this.collector) {
            document.getElementById('totalInteractions').textContent =
                `${this.collector.interactions.length} interactions`;
        }
    }

    showExportModal() {
        if (!this.currentSession) {
            this.showNotification('No session to export', 'warning');
            return;
        }

        // Set default values
        document.getElementById('startTime').value = 0;
        document.getElementById('endTime').value = this.sessionDuration;

        document.getElementById('exportModal').classList.add('show');
    }

    hideExportModal() {
        document.getElementById('exportModal').classList.remove('show');
    }

    exportSession() {
        if (!this.currentSession) return;

        const format = document.getElementById('exportFormat').value;
        const startTime = parseInt(document.getElementById('startTime').value) || 0;
        const endTime = parseInt(document.getElementById('endTime').value) || this.sessionDuration;

        const includeTracks = {
            mouse: document.getElementById('includeMouse').checked,
            click: document.getElementById('includeClick').checked,
            keyboard: document.getElementById('includeKeyboard').checked,
            scroll: document.getElementById('includeScroll').checked
        };

        // Filter interactions by time range and track selection
        let filteredInteractions = this.currentSession.interactions.filter(interaction => {
            if (interaction.timestamp < startTime || interaction.timestamp > endTime) {
                return false;
            }

            switch (interaction.type) {
                case 'mousemove':
                    return includeTracks.mouse;
                case 'click':
                    return includeTracks.click;
                case 'keydown':
                case 'keyup':
                    return includeTracks.keyboard;
                case 'wheel':
                    return includeTracks.scroll;
                default:
                    return true;
            }
        });

        const exportData = {
            ...this.currentSession,
            interactions: filteredInteractions,
            exportSettings: {
                format,
                timeRange: { start: startTime, end: endTime },
                includedTracks: includeTracks,
                exportTimestamp: new Date().toISOString()
            }
        };

        let filename, content;

        switch (format) {
            case 'json':
                filename = `timeline-session-${this.currentSession.session.id.substring(0, 8)}.json`;
                content = JSON.stringify(exportData, null, 2);
                break;
            case 'csv':
                filename = `timeline-session-${this.currentSession.session.id.substring(0, 8)}.csv`;
                content = this.convertToCSV(filteredInteractions);
                break;
            case 'timeline':
                filename = `timeline-project-${this.currentSession.session.id.substring(0, 8)}.tlp`;
                content = JSON.stringify(exportData, null, 2);
                break;
        }

        UIUtils.downloadFile(content, filename);
        this.hideExportModal();
        this.showNotification('Session exported successfully', 'success');
    }

    convertToCSV(interactions) {
        const headers = ['timestamp', 'type', 'x', 'y', 'button', 'key', 'velocity', 'acceleration'];
        const rows = [headers.join(',')];

        interactions.forEach(interaction => {
            const row = [
                interaction.timestamp,
                interaction.type,
                interaction.data.x || '',
                interaction.data.y || '',
                interaction.data.button !== undefined ? interaction.data.button : '',
                interaction.data.key || '',
                interaction.data.velocity || '',
                interaction.data.acceleration || ''
            ];
            rows.push(row.join(','));
        });

        return rows.join('\n');
    }

    showNotification(message, type = 'info') {
        // Simple notification system
        console.log(`[${type.toUpperCase()}] ${message}`);

        // You could implement a toast notification system here
        // For now, we'll use console logging
    }
}

// Initialize timeline recorder when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.timelineRecorder = new TimelineRecorderController();
});

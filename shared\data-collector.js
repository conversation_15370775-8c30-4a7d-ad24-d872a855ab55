/**
 * Human Interaction Data Collector
 * Core engine for capturing mouse, keyboard, and scroll interactions
 * Optimized for AI/ML training data generation
 */

class HumanInteractionCollector {
    constructor(options = {}) {
        this.isRecording = false;
        this.sessionId = this.generateUUID();
        this.startTime = null;
        this.interactions = [];
        this.lastMousePosition = { x: 0, y: 0 };
        this.lastMouseTime = 0;
        this.scrollData = [];
        this.keystrokes = [];
        
        // Configuration
        this.config = {
            sampleRate: options.sampleRate || 60, // Hz
            captureVelocity: options.captureVelocity !== false,
            captureAcceleration: options.captureAcceleration !== false,
            captureScrollMomentum: options.captureScrollMomentum !== false,
            captureTypingRhythm: options.captureTypingRhythm !== false,
            ...options
        };
        
        this.callbacks = {
            onStart: options.onStart || (() => {}),
            onStop: options.onStop || (() => {}),
            onDataPoint: options.onDataPoint || (() => {}),
            onError: options.onError || console.error
        };
        
        this.bindEvents();
    }
    
    generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
    
    bindEvents() {
        // Mouse movement tracking
        this.mouseMoveHandler = (e) => this.handleMouseMove(e);
        this.mouseClickHandler = (e) => this.handleMouseClick(e);
        this.scrollHandler = (e) => this.handleScroll(e);
        this.keydownHandler = (e) => this.handleKeyDown(e);
        this.keyupHandler = (e) => this.handleKeyUp(e);
    }
    
    startRecording() {
        if (this.isRecording) return false;
        
        this.isRecording = true;
        this.startTime = Date.now();
        this.interactions = [];
        this.sessionId = this.generateUUID();
        
        // Add event listeners
        document.addEventListener('mousemove', this.mouseMoveHandler, { passive: true });
        document.addEventListener('click', this.mouseClickHandler, { passive: true });
        document.addEventListener('scroll', this.scrollHandler, { passive: true });
        document.addEventListener('keydown', this.keydownHandler, { passive: true });
        document.addEventListener('keyup', this.keyupHandler, { passive: true });
        
        this.callbacks.onStart(this.sessionId);
        return true;
    }
    
    stopRecording() {
        if (!this.isRecording) return null;
        
        this.isRecording = false;
        
        // Remove event listeners
        document.removeEventListener('mousemove', this.mouseMoveHandler);
        document.removeEventListener('click', this.mouseClickHandler);
        document.removeEventListener('scroll', this.scrollHandler);
        document.removeEventListener('keydown', this.keydownHandler);
        document.removeEventListener('keyup', this.keyupHandler);
        
        const sessionData = this.generateSessionData();
        this.callbacks.onStop(sessionData);
        return sessionData;
    }
    
    handleMouseMove(event) {
        if (!this.isRecording) return;
        
        const now = Date.now();
        const currentPos = { x: event.clientX, y: event.clientY };
        
        let velocity = { x: 0, y: 0 };
        let acceleration = { x: 0, y: 0 };
        
        if (this.config.captureVelocity && this.lastMouseTime > 0) {
            const deltaTime = (now - this.lastMouseTime) / 1000; // seconds
            if (deltaTime > 0) {
                velocity.x = (currentPos.x - this.lastMousePosition.x) / deltaTime;
                velocity.y = (currentPos.y - this.lastMousePosition.y) / deltaTime;
            }
        }
        
        const interaction = {
            type: 'mouse_move',
            timestamp: now - this.startTime,
            x: currentPos.x,
            y: currentPos.y,
            screenX: event.screenX,
            screenY: event.screenY,
            velocity: this.config.captureVelocity ? velocity : undefined,
            target: this.getElementInfo(event.target)
        };
        
        this.interactions.push(interaction);
        this.callbacks.onDataPoint(interaction);
        
        this.lastMousePosition = currentPos;
        this.lastMouseTime = now;
    }
    
    handleMouseClick(event) {
        if (!this.isRecording) return;
        
        const interaction = {
            type: 'mouse_click',
            timestamp: Date.now() - this.startTime,
            x: event.clientX,
            y: event.clientY,
            button: event.button,
            detail: event.detail, // click count
            target: this.getElementInfo(event.target)
        };
        
        this.interactions.push(interaction);
        this.callbacks.onDataPoint(interaction);
    }
    
    handleScroll(event) {
        if (!this.isRecording) return;
        
        const interaction = {
            type: 'scroll',
            timestamp: Date.now() - this.startTime,
            scrollX: window.scrollX,
            scrollY: window.scrollY,
            deltaX: event.deltaX || 0,
            deltaY: event.deltaY || 0,
            deltaMode: event.deltaMode,
            target: this.getElementInfo(event.target)
        };
        
        this.interactions.push(interaction);
        this.callbacks.onDataPoint(interaction);
    }
    
    handleKeyDown(event) {
        if (!this.isRecording) return;
        
        const interaction = {
            type: 'key_down',
            timestamp: Date.now() - this.startTime,
            key: event.key,
            code: event.code,
            keyCode: event.keyCode,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            altKey: event.altKey,
            metaKey: event.metaKey,
            target: this.getElementInfo(event.target)
        };
        
        this.interactions.push(interaction);
        this.callbacks.onDataPoint(interaction);
    }
    
    handleKeyUp(event) {
        if (!this.isRecording) return;
        
        const interaction = {
            type: 'key_up',
            timestamp: Date.now() - this.startTime,
            key: event.key,
            code: event.code,
            keyCode: event.keyCode,
            target: this.getElementInfo(event.target)
        };
        
        this.interactions.push(interaction);
        this.callbacks.onDataPoint(interaction);
    }
    
    getElementInfo(element) {
        if (!element) return null;
        
        return {
            tagName: element.tagName,
            id: element.id || null,
            className: element.className || null,
            textContent: element.textContent ? element.textContent.substring(0, 100) : null,
            attributes: this.getRelevantAttributes(element)
        };
    }
    
    getRelevantAttributes(element) {
        const relevantAttrs = ['type', 'name', 'placeholder', 'href', 'src', 'alt', 'title'];
        const attrs = {};
        
        relevantAttrs.forEach(attr => {
            if (element.hasAttribute(attr)) {
                attrs[attr] = element.getAttribute(attr);
            }
        });
        
        return Object.keys(attrs).length > 0 ? attrs : null;
    }
    
    generateSessionData() {
        return {
            session: {
                id: this.sessionId,
                timestamp: new Date(this.startTime).toISOString(),
                duration: Date.now() - this.startTime,
                url: window.location.href,
                userAgent: navigator.userAgent,
                viewport: {
                    width: window.innerWidth,
                    height: window.innerHeight
                },
                screen: {
                    width: screen.width,
                    height: screen.height,
                    pixelRatio: window.devicePixelRatio
                }
            },
            interactions: this.interactions,
            metadata: {
                totalInteractions: this.interactions.length,
                interactionTypes: this.getInteractionTypeCounts(),
                config: this.config
            }
        };
    }
    
    getInteractionTypeCounts() {
        const counts = {};
        this.interactions.forEach(interaction => {
            counts[interaction.type] = (counts[interaction.type] || 0) + 1;
        });
        return counts;
    }
    
    exportData(format = 'json') {
        const data = this.generateSessionData();
        
        switch (format) {
            case 'json':
                return JSON.stringify(data, null, 2);
            case 'csv':
                return this.convertToCSV(data);
            default:
                throw new Error(`Unsupported export format: ${format}`);
        }
    }
    
    convertToCSV(data) {
        const headers = ['timestamp', 'type', 'x', 'y', 'key', 'target_tag', 'target_id'];
        const rows = [headers.join(',')];
        
        data.interactions.forEach(interaction => {
            const row = [
                interaction.timestamp,
                interaction.type,
                interaction.x || '',
                interaction.y || '',
                interaction.key || '',
                interaction.target?.tagName || '',
                interaction.target?.id || ''
            ];
            rows.push(row.join(','));
        });
        
        return rows.join('\n');
    }
}

// Export for use in different module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HumanInteractionCollector;
} else if (typeof window !== 'undefined') {
    window.HumanInteractionCollector = HumanInteractionCollector;
}

/**
 * Analytics Dashboard Styles
 */

body {
    margin: 0;
    padding: 0;
    background: #f8fafc;
    color: #2d3748;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    overflow: hidden;
}

.dashboard-container {
    display: flex;
    height: 100vh;
}

/* Sidebar */
.dashboard-sidebar {
    width: 280px;
    background: #2d3748;
    color: white;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
    padding: 24px 20px;
    border-bottom: 1px solid #4a5568;
}

.app-title {
    margin: 0;
    font-size: 20px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
}

.title-icon {
    font-size: 24px;
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.nav-section {
    margin-bottom: 32px;
}

.nav-title {
    margin: 0 0 12px 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #a0aec0;
}

.nav-item {
    width: 100%;
    padding: 12px 20px;
    border: none;
    background: none;
    color: white;
    text-align: left;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.nav-item:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.1);
}

.nav-item:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.nav-item.active {
    background: #4299e1;
    box-shadow: inset 3px 0 0 #63b3ed;
}

.nav-item.record-btn {
    background: linear-gradient(135deg, #e53e3e, #c53030);
    margin: 0 20px;
    border-radius: 8px;
    font-weight: 600;
}

.nav-item.record-btn:hover {
    background: linear-gradient(135deg, #c53030, #9c2626);
}

.nav-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.nav-text {
    flex: 1;
}

.session-list {
    max-height: 200px;
    overflow-y: auto;
}

.session-item {
    padding: 8px 20px;
    cursor: pointer;
    font-size: 13px;
    color: #cbd5e0;
    transition: all 0.2s ease;
}

.session-item:hover {
    background: rgba(255, 255, 255, 0.05);
    color: white;
}

.session-item.active {
    background: rgba(66, 153, 225, 0.2);
    color: #63b3ed;
}

.empty-sessions {
    padding: 20px;
    text-align: center;
    color: #718096;
    font-style: italic;
    font-size: 13px;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid #4a5568;
}

.recording-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #68d391;
    transition: all 0.3s ease;
}

.status-dot.recording {
    background: #f56565;
    animation: pulse 1.5s infinite;
}

.status-dot.paused {
    background: #ed8936;
}

.status-text {
    font-size: 12px;
    color: #a0aec0;
}

.recording-timer {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    font-weight: bold;
    color: #4299e1;
}

/* Main Content */
.dashboard-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.dashboard-header {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    padding: 20px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.header-left {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.page-title {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    color: #1a202c;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #718096;
}

.separator {
    color: #cbd5e0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.view-controls {
    display: flex;
    background: #f7fafc;
    border-radius: 8px;
    padding: 4px;
    gap: 2px;
}

.view-btn {
    padding: 8px 12px;
    border: none;
    background: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.2s ease;
}

.view-btn:hover {
    background: #e2e8f0;
}

.view-btn.active {
    background: #4299e1;
    color: white;
    box-shadow: 0 2px 4px rgba(66, 153, 225, 0.3);
}

.time-range-selector select {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    color: #2d3748;
}

/* Dashboard Content */
.dashboard-content {
    flex: 1;
    overflow: hidden;
    position: relative;
}

.view-panel {
    display: none;
    height: 100%;
    overflow-y: auto;
    padding: 24px;
}

.view-panel.active {
    display: block;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

.metric-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.metric-title {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-icon {
    font-size: 20px;
    opacity: 0.7;
}

.metric-value {
    font-size: 28px;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 4px;
}

.metric-change {
    font-size: 12px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
}

.metric-change.positive {
    color: #38a169;
    background: #f0fff4;
}

.metric-change.negative {
    color: #e53e3e;
    background: #fed7d7;
}

/* Charts Grid */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.chart-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.chart-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8fafc;
}

.chart-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
}

.chart-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.chart-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: #f7fafc;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.2s ease;
}

.chart-btn:hover {
    background: #e2e8f0;
}

.chart-select {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
}

.chart-container {
    padding: 20px;
    height: 300px;
    position: relative;
}

.chart-container canvas {
    width: 100% !important;
    height: 100% !important;
}

/* Heatmap View */
.heatmap-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.heatmap-header {
    padding: 20px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8fafc;
}

.heatmap-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
}

.heatmap-controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

.heatmap-controls label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #4a5568;
}

.heatmap-canvas-container {
    position: relative;
    height: calc(100vh - 200px);
    background: #f8fafc;
}

#heatmapCanvas {
    width: 100%;
    height: 100%;
    display: block;
}

.heatmap-legend {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.9);
    padding: 12px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.legend-title {
    font-size: 12px;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 8px;
}

.legend-gradient {
    width: 100px;
    height: 12px;
    background: linear-gradient(to right, #4299e1, #f56565);
    border-radius: 6px;
    margin-bottom: 4px;
}

.legend-labels {
    display: flex;
    justify-content: space-between;
    font-size: 10px;
    color: #718096;
}

/* Pattern Analysis */
.patterns-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.pattern-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.pattern-card h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
}

.pattern-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.pattern-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f7fafc;
}

.pattern-name {
    font-size: 14px;
    color: #4a5568;
}

.pattern-frequency {
    font-size: 14px;
    font-weight: 600;
    color: #4299e1;
}

.pattern-visualization {
    height: 200px;
}

.insights-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.insight-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 3px solid #4299e1;
}

.insight-icon {
    font-size: 16px;
}

.insight-text {
    font-size: 14px;
    color: #4a5568;
}

/* Comparison View */
.comparison-header {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.comparison-header h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
}

.comparison-controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

.comparison-controls select {
    flex: 1;
    max-width: 200px;
}

.vs-label {
    font-weight: 600;
    color: #718096;
}

.comparison-results {
    background: white;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.comparison-placeholder {
    text-align: center;
    color: #718096;
    font-style: italic;
}

/* Animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        flex-direction: column;
    }
    
    .dashboard-sidebar {
        width: 100%;
        height: auto;
        max-height: 200px;
    }
    
    .sidebar-nav {
        display: flex;
        overflow-x: auto;
        padding: 10px 0;
    }
    
    .nav-section {
        margin-bottom: 0;
        margin-right: 20px;
        flex-shrink: 0;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .header-right {
        justify-content: space-between;
    }
}

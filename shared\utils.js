/**
 * Shared Utility Functions
 * Common functionality used across all UI variants
 */

class UIUtils {
    // File download functionality
    static downloadFile(content, filename, contentType = 'application/json') {
        const blob = new Blob([content], { type: contentType });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }
    
    // Format timestamp for display
    static formatTimestamp(timestamp) {
        return new Date(timestamp).toLocaleString();
    }
    
    // Format duration in milliseconds to human readable
    static formatDuration(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        } else {
            return `${seconds}s`;
        }
    }
    
    // Format file size
    static formatFileSize(bytes) {
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        if (bytes === 0) return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }
    
    // Generate session filename
    static generateSessionFilename(sessionId, format = 'json') {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        return `interaction-session-${sessionId.substring(0, 8)}-${timestamp}.${format}`;
    }
    
    // Local storage helpers
    static saveToLocalStorage(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('Failed to save to localStorage:', error);
            return false;
        }
    }
    
    static loadFromLocalStorage(key) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('Failed to load from localStorage:', error);
            return null;
        }
    }
    
    // Session management
    static getSavedSessions() {
        const sessions = this.loadFromLocalStorage('interaction_sessions') || [];
        return sessions.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    }
    
    static saveSession(sessionData) {
        const sessions = this.getSavedSessions();
        sessions.unshift({
            id: sessionData.session.id,
            timestamp: sessionData.session.timestamp,
            duration: sessionData.session.duration,
            url: sessionData.session.url,
            interactionCount: sessionData.interactions.length,
            size: JSON.stringify(sessionData).length
        });
        
        // Keep only last 50 sessions
        const trimmedSessions = sessions.slice(0, 50);
        this.saveToLocalStorage('interaction_sessions', trimmedSessions);
        
        // Save full session data
        this.saveToLocalStorage(`session_${sessionData.session.id}`, sessionData);
    }
    
    static loadSession(sessionId) {
        return this.loadFromLocalStorage(`session_${sessionId}`);
    }
    
    static deleteSession(sessionId) {
        // Remove from sessions list
        const sessions = this.getSavedSessions();
        const filteredSessions = sessions.filter(s => s.id !== sessionId);
        this.saveToLocalStorage('interaction_sessions', filteredSessions);
        
        // Remove full session data
        localStorage.removeItem(`session_${sessionId}`);
    }
    
    // UI state management
    static saveUIState(uiName, state) {
        this.saveToLocalStorage(`ui_state_${uiName}`, state);
    }
    
    static loadUIState(uiName) {
        return this.loadFromLocalStorage(`ui_state_${uiName}`) || {};
    }
    
    // Notification system
    static showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '4px',
            color: 'white',
            fontWeight: 'bold',
            zIndex: '10000',
            opacity: '0',
            transition: 'opacity 0.3s ease',
            backgroundColor: this.getNotificationColor(type)
        });
        
        document.body.appendChild(notification);
        
        // Fade in
        setTimeout(() => notification.style.opacity = '1', 10);
        
        // Auto remove
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);
    }
    
    static getNotificationColor(type) {
        const colors = {
            info: '#3498db',
            success: '#2ecc71',
            warning: '#f39c12',
            error: '#e74c3c'
        };
        return colors[type] || colors.info;
    }
    
    // Keyboard shortcut handler
    static setupKeyboardShortcuts(shortcuts) {
        document.addEventListener('keydown', (event) => {
            const key = this.getKeyCombo(event);
            if (shortcuts[key]) {
                event.preventDefault();
                shortcuts[key]();
            }
        });
    }
    
    static getKeyCombo(event) {
        const parts = [];
        if (event.ctrlKey) parts.push('ctrl');
        if (event.altKey) parts.push('alt');
        if (event.shiftKey) parts.push('shift');
        if (event.metaKey) parts.push('meta');
        parts.push(event.key.toLowerCase());
        return parts.join('+');
    }
    
    // Performance monitoring
    static measurePerformance(name, fn) {
        const start = performance.now();
        const result = fn();
        const end = performance.now();
        console.log(`${name} took ${end - start} milliseconds`);
        return result;
    }
    
    // Data validation
    static validateSessionData(data) {
        const errors = [];
        
        if (!data.session) {
            errors.push('Missing session metadata');
        } else {
            if (!data.session.id) errors.push('Missing session ID');
            if (!data.session.timestamp) errors.push('Missing session timestamp');
            if (typeof data.session.duration !== 'number') errors.push('Invalid session duration');
        }
        
        if (!Array.isArray(data.interactions)) {
            errors.push('Interactions must be an array');
        } else if (data.interactions.length === 0) {
            errors.push('No interactions recorded');
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
    
    // Theme management
    static applyTheme(themeName) {
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        document.body.classList.add(`theme-${themeName}`);
        this.saveToLocalStorage('selected_theme', themeName);
    }
    
    static loadSavedTheme() {
        const savedTheme = this.loadFromLocalStorage('selected_theme');
        if (savedTheme) {
            this.applyTheme(savedTheme);
        }
        return savedTheme || 'default';
    }
    
    // URL validation
    static isValidURL(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }
    
    // Debounce function
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Throttle function
    static throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// Export for use in different module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UIUtils;
} else if (typeof window !== 'undefined') {
    window.UIUtils = UIUtils;
}

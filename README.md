# Human Interaction Recording System - UI Collection

A collection of 10 distinct web application user interfaces designed for recording human web interactions to train AI models for automated web browsing with nodriver.

## Overview

This system captures detailed behavioral data including:
- **Mouse Movements**: Coordinates, velocity, acceleration curves
- **Scrolling Behavior**: Speed, direction, momentum patterns
- **Typing Patterns**: Keystroke timing, rhythm, correction patterns
- **Context Data**: Website interactions, session metadata, user intent

## UI Variants

### 1. Dashboard Control Center (`ui1-dashboard/`)
Comprehensive dashboard with real-time visualization and session management.

### 2. Minimalist Floating Panel (`ui2-floating/`)
Compact, draggable overlay panel with essential controls.

### 3. Timeline-Based Recorder (`ui3-timeline/`)
Timeline-centric interface with playback and analysis capabilities.

### 4. Gesture-Controlled Interface (`ui4-gesture/`)
Mouse gesture and keyboard shortcut controlled interface.

### 5. Wizard-Style Guided Recorder (`ui5-wizard/`)
Step-by-step guided recording process.

### 6. Analytics-Focused Interface (`ui6-analytics/`)
Real-time analytics with heatmaps and pattern visualization.

### 7. Mobile-First Responsive Design (`ui7-mobile/`)
Touch-optimized interface for mobile devices.

### 8. Developer Console Style (`ui8-console/`)
Developer-friendly interface with detailed logging.

### 9. Gamified Recording Interface (`ui9-gamified/`)
Gamified experience with progress tracking and achievements.

### 10. AI Assistant Integrated (`ui10-ai/`)
AI-powered interface with real-time feedback and suggestions.

## Shared Components

- `shared/`: Common utilities, data collection engine, and export functions
- `docs/`: Technical documentation and ML integration guides

## Data Format

All interfaces export data in a standardized JSON format optimized for ML training:

```json
{
  "session": {
    "id": "uuid",
    "timestamp": "ISO-8601",
    "duration": "milliseconds",
    "url": "target-website",
    "userAgent": "browser-info",
    "viewport": {"width": 1920, "height": 1080}
  },
  "interactions": [
    {
      "type": "mouse_move",
      "timestamp": 1234567890,
      "x": 100,
      "y": 200,
      "velocity": {"x": 5.2, "y": -2.1},
      "acceleration": {"x": 0.5, "y": -0.3}
    }
  ]
}
```

## Usage

Each UI can be run independently by opening its `index.html` file in a web browser. For production use, serve through a web server to enable full functionality.

## For ML Engineers

See `docs/ml-integration.md` for detailed information about:
- Data schema and format specifications
- Integration with training pipelines
- Quality metrics and validation
- Nodriver compatibility considerations

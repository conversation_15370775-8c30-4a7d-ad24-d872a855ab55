<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics Dashboard - Human Interaction Recorder</title>
    <link rel="stylesheet" href="../shared/styles.css">
    <link rel="stylesheet" href="dashboard.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="dashboard-sidebar">
            <div class="sidebar-header">
                <h1 class="app-title">
                    <span class="title-icon">📊</span>
                    Analytics
                </h1>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <h3 class="nav-title">Recording</h3>
                    <button class="nav-item record-btn" id="recordBtn">
                        <span class="nav-icon">🔴</span>
                        <span class="nav-text">Start Recording</span>
                    </button>
                    <button class="nav-item" id="pauseBtn" disabled>
                        <span class="nav-icon">⏸️</span>
                        <span class="nav-text">Pause</span>
                    </button>
                    <button class="nav-item" id="stopBtn" disabled>
                        <span class="nav-icon">⏹️</span>
                        <span class="nav-text">Stop</span>
                    </button>
                </div>
                
                <div class="nav-section">
                    <h3 class="nav-title">Sessions</h3>
                    <div class="session-list" id="sessionList">
                        <div class="empty-sessions">No sessions available</div>
                    </div>
                </div>
                
                <div class="nav-section">
                    <h3 class="nav-title">Export</h3>
                    <button class="nav-item" id="exportCurrentBtn">
                        <span class="nav-icon">📤</span>
                        <span class="nav-text">Export Current</span>
                    </button>
                    <button class="nav-item" id="exportAllBtn">
                        <span class="nav-icon">📦</span>
                        <span class="nav-text">Export All</span>
                    </button>
                </div>
            </nav>
            
            <div class="sidebar-footer">
                <div class="recording-status" id="recordingStatus">
                    <div class="status-indicator">
                        <div class="status-dot"></div>
                        <span class="status-text">Ready</span>
                    </div>
                    <div class="recording-timer" id="recordingTimer">00:00</div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Header -->
            <header class="dashboard-header">
                <div class="header-left">
                    <h2 class="page-title" id="pageTitle">Real-time Analytics</h2>
                    <div class="breadcrumb" id="breadcrumb">
                        <span>Dashboard</span>
                        <span class="separator">›</span>
                        <span>Live Session</span>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="view-controls">
                        <button class="view-btn active" data-view="realtime" title="Real-time View">
                            <span>📈</span>
                        </button>
                        <button class="view-btn" data-view="heatmap" title="Heatmap View">
                            <span>🔥</span>
                        </button>
                        <button class="view-btn" data-view="patterns" title="Pattern Analysis">
                            <span>🧩</span>
                        </button>
                        <button class="view-btn" data-view="comparison" title="Session Comparison">
                            <span>⚖️</span>
                        </button>
                    </div>
                    
                    <div class="time-range-selector">
                        <select id="timeRange" class="form-control">
                            <option value="live">Live</option>
                            <option value="1m">Last 1 minute</option>
                            <option value="5m">Last 5 minutes</option>
                            <option value="15m">Last 15 minutes</option>
                            <option value="1h">Last 1 hour</option>
                            <option value="all">All time</option>
                        </select>
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <div class="dashboard-content" id="dashboardContent">
                <!-- Real-time View -->
                <div class="view-panel active" id="realtimeView">
                    <!-- Key Metrics -->
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-header">
                                <h3 class="metric-title">Interactions/sec</h3>
                                <span class="metric-icon">⚡</span>
                            </div>
                            <div class="metric-value" id="interactionRate">0</div>
                            <div class="metric-change positive" id="interactionChange">+0%</div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-header">
                                <h3 class="metric-title">Mouse Velocity</h3>
                                <span class="metric-icon">🖱️</span>
                            </div>
                            <div class="metric-value" id="mouseVelocity">0 px/s</div>
                            <div class="metric-change" id="velocityChange">+0%</div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-header">
                                <h3 class="metric-title">Click Frequency</h3>
                                <span class="metric-icon">👆</span>
                            </div>
                            <div class="metric-value" id="clickFrequency">0/min</div>
                            <div class="metric-change" id="clickChange">+0%</div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-header">
                                <h3 class="metric-title">Session Duration</h3>
                                <span class="metric-icon">⏱️</span>
                            </div>
                            <div class="metric-value" id="sessionDuration">00:00</div>
                            <div class="metric-change" id="durationChange">Active</div>
                        </div>
                    </div>

                    <!-- Charts Grid -->
                    <div class="charts-grid">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3 class="chart-title">Interaction Timeline</h3>
                                <div class="chart-controls">
                                    <button class="chart-btn" id="pauseChart" title="Pause/Resume">⏸️</button>
                                    <button class="chart-btn" id="clearChart" title="Clear">🗑️</button>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="timelineChart"></canvas>
                            </div>
                        </div>
                        
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3 class="chart-title">Mouse Movement</h3>
                                <div class="chart-controls">
                                    <button class="chart-btn" id="resetMouseChart" title="Reset View">🔄</button>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="mouseChart"></canvas>
                            </div>
                        </div>
                        
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3 class="chart-title">Event Distribution</h3>
                                <div class="chart-controls">
                                    <select class="chart-select" id="distributionType">
                                        <option value="type">By Type</option>
                                        <option value="time">By Time</option>
                                        <option value="velocity">By Velocity</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="distributionChart"></canvas>
                            </div>
                        </div>
                        
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3 class="chart-title">Performance Metrics</h3>
                                <div class="chart-controls">
                                    <button class="chart-btn" id="exportMetrics" title="Export Data">📊</button>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="performanceChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Heatmap View -->
                <div class="view-panel" id="heatmapView">
                    <div class="heatmap-container">
                        <div class="heatmap-header">
                            <h3>Mouse Movement Heatmap</h3>
                            <div class="heatmap-controls">
                                <label>
                                    Intensity:
                                    <input type="range" id="heatmapIntensity" min="0.1" max="2" step="0.1" value="1">
                                </label>
                                <label>
                                    Radius:
                                    <input type="range" id="heatmapRadius" min="5" max="50" step="5" value="20">
                                </label>
                                <button class="btn btn-secondary" id="resetHeatmap">Reset</button>
                            </div>
                        </div>
                        <div class="heatmap-canvas-container">
                            <canvas id="heatmapCanvas"></canvas>
                            <div class="heatmap-legend">
                                <div class="legend-title">Activity Level</div>
                                <div class="legend-gradient"></div>
                                <div class="legend-labels">
                                    <span>Low</span>
                                    <span>High</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pattern Analysis View -->
                <div class="view-panel" id="patternsView">
                    <div class="patterns-grid">
                        <div class="pattern-card">
                            <h3>Movement Patterns</h3>
                            <div class="pattern-list" id="movementPatterns">
                                <div class="pattern-item">
                                    <span class="pattern-name">Linear Movement</span>
                                    <span class="pattern-frequency">45%</span>
                                </div>
                                <div class="pattern-item">
                                    <span class="pattern-name">Circular Movement</span>
                                    <span class="pattern-frequency">23%</span>
                                </div>
                                <div class="pattern-item">
                                    <span class="pattern-name">Erratic Movement</span>
                                    <span class="pattern-frequency">32%</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="pattern-card">
                            <h3>Click Patterns</h3>
                            <div class="pattern-list" id="clickPatterns">
                                <div class="pattern-item">
                                    <span class="pattern-name">Single Clicks</span>
                                    <span class="pattern-frequency">78%</span>
                                </div>
                                <div class="pattern-item">
                                    <span class="pattern-name">Double Clicks</span>
                                    <span class="pattern-frequency">18%</span>
                                </div>
                                <div class="pattern-item">
                                    <span class="pattern-name">Drag Operations</span>
                                    <span class="pattern-frequency">4%</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="pattern-card">
                            <h3>Temporal Patterns</h3>
                            <div class="pattern-visualization">
                                <canvas id="temporalChart"></canvas>
                            </div>
                        </div>
                        
                        <div class="pattern-card">
                            <h3>Behavioral Insights</h3>
                            <div class="insights-list" id="behavioralInsights">
                                <div class="insight-item">
                                    <span class="insight-icon">🎯</span>
                                    <span class="insight-text">High precision in target selection</span>
                                </div>
                                <div class="insight-item">
                                    <span class="insight-icon">⚡</span>
                                    <span class="insight-text">Fast interaction pace detected</span>
                                </div>
                                <div class="insight-item">
                                    <span class="insight-icon">🔄</span>
                                    <span class="insight-text">Repetitive movement patterns</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Comparison View -->
                <div class="view-panel" id="comparisonView">
                    <div class="comparison-header">
                        <h3>Session Comparison</h3>
                        <div class="comparison-controls">
                            <select id="session1Select" class="form-control">
                                <option value="">Select Session 1</option>
                            </select>
                            <span class="vs-label">vs</span>
                            <select id="session2Select" class="form-control">
                                <option value="">Select Session 2</option>
                            </select>
                            <button class="btn btn-primary" id="compareBtn">Compare</button>
                        </div>
                    </div>
                    
                    <div class="comparison-results" id="comparisonResults">
                        <div class="comparison-placeholder">
                            Select two sessions to compare their metrics and patterns.
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="../shared/data-collector.js"></script>
    <script src="../shared/utils.js"></script>
    <script src="dashboard.js"></script>
</body>
</html>

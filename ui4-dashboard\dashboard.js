/**
 * Analytics Dashboard Controller
 * Real-time data visualization and analysis interface
 */

class AnalyticsDashboardController {
    constructor() {
        this.collector = null;
        this.isRecording = false;
        this.isPaused = false;
        this.currentSession = null;
        this.sessions = [];
        this.currentView = 'realtime';
        
        // Chart instances
        this.charts = {};
        this.chartsPaused = false;
        
        // Real-time data
        this.realtimeData = {
            interactions: [],
            mousePositions: [],
            velocities: [],
            clickTimes: [],
            keyTimes: []
        };
        
        // Metrics tracking
        this.metrics = {
            interactionRate: 0,
            mouseVelocity: 0,
            clickFrequency: 0,
            sessionDuration: 0
        };
        
        // Update intervals
        this.metricsInterval = null;
        this.chartsInterval = null;
        
        this.initializeUI();
        this.bindEvents();
        this.setupCharts();
        this.loadSessions();
    }
    
    initializeUI() {
        // Initialize view
        this.showView('realtime');
        
        // Load saved sessions
        this.refreshSessionList();
        
        // Setup heatmap canvas
        this.setupHeatmap();
    }
    
    bindEvents() {
        // Recording controls
        document.getElementById('recordBtn').addEventListener('click', () => this.toggleRecording());
        document.getElementById('pauseBtn').addEventListener('click', () => this.pauseRecording());
        document.getElementById('stopBtn').addEventListener('click', () => this.stopRecording());
        
        // Export controls
        document.getElementById('exportCurrentBtn').addEventListener('click', () => this.exportCurrentSession());
        document.getElementById('exportAllBtn').addEventListener('click', () => this.exportAllSessions());
        
        // View controls
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const view = e.target.dataset.view;
                this.showView(view);
            });
        });
        
        // Time range selector
        document.getElementById('timeRange').addEventListener('change', (e) => {
            this.updateTimeRange(e.target.value);
        });
        
        // Chart controls
        this.bindChartControls();
        
        // Heatmap controls
        this.bindHeatmapControls();
        
        // Comparison controls
        this.bindComparisonControls();
    }
    
    bindChartControls() {
        document.getElementById('pauseChart').addEventListener('click', () => {
            this.chartsPaused = !this.chartsPaused;
            const btn = document.getElementById('pauseChart');
            btn.textContent = this.chartsPaused ? '▶️' : '⏸️';
        });
        
        document.getElementById('clearChart').addEventListener('click', () => {
            this.clearChartData();
        });
        
        document.getElementById('resetMouseChart').addEventListener('click', () => {
            this.resetMouseChart();
        });
        
        document.getElementById('distributionType').addEventListener('change', (e) => {
            this.updateDistributionChart(e.target.value);
        });
        
        document.getElementById('exportMetrics').addEventListener('click', () => {
            this.exportMetricsData();
        });
    }
    
    bindHeatmapControls() {
        document.getElementById('heatmapIntensity').addEventListener('input', (e) => {
            this.updateHeatmapSettings();
        });
        
        document.getElementById('heatmapRadius').addEventListener('input', (e) => {
            this.updateHeatmapSettings();
        });
        
        document.getElementById('resetHeatmap').addEventListener('click', () => {
            this.resetHeatmap();
        });
    }
    
    bindComparisonControls() {
        document.getElementById('compareBtn').addEventListener('click', () => {
            this.compareSelectedSessions();
        });
    }
    
    setupCharts() {
        // Timeline Chart
        const timelineCtx = document.getElementById('timelineChart').getContext('2d');
        this.charts.timeline = new Chart(timelineCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Interactions/sec',
                    data: [],
                    borderColor: '#4299e1',
                    backgroundColor: 'rgba(66, 153, 225, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'second'
                        }
                    },
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
        
        // Mouse Movement Chart
        const mouseCtx = document.getElementById('mouseChart').getContext('2d');
        this.charts.mouse = new Chart(mouseCtx, {
            type: 'scatter',
            data: {
                datasets: [{
                    label: 'Mouse Position',
                    data: [],
                    backgroundColor: 'rgba(66, 153, 225, 0.6)',
                    borderColor: '#4299e1',
                    pointRadius: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        type: 'linear',
                        position: 'bottom',
                        min: 0,
                        max: window.innerWidth
                    },
                    y: {
                        min: 0,
                        max: window.innerHeight,
                        reverse: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
        
        // Distribution Chart
        const distributionCtx = document.getElementById('distributionChart').getContext('2d');
        this.charts.distribution = new Chart(distributionCtx, {
            type: 'doughnut',
            data: {
                labels: ['Mouse', 'Click', 'Keyboard', 'Scroll'],
                datasets: [{
                    data: [0, 0, 0, 0],
                    backgroundColor: [
                        '#4299e1',
                        '#f56565',
                        '#48bb78',
                        '#ed8936'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        
        // Performance Chart
        const performanceCtx = document.getElementById('performanceChart').getContext('2d');
        this.charts.performance = new Chart(performanceCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'Mouse Velocity',
                        data: [],
                        borderColor: '#4299e1',
                        backgroundColor: 'rgba(66, 153, 225, 0.1)',
                        yAxisID: 'y'
                    },
                    {
                        label: 'Click Rate',
                        data: [],
                        borderColor: '#f56565',
                        backgroundColor: 'rgba(245, 101, 101, 0.1)',
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'second'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });
        
        // Temporal Pattern Chart
        const temporalCtx = document.getElementById('temporalChart').getContext('2d');
        this.charts.temporal = new Chart(temporalCtx, {
            type: 'bar',
            data: {
                labels: Array.from({length: 24}, (_, i) => `${i}:00`),
                datasets: [{
                    label: 'Activity by Hour',
                    data: new Array(24).fill(0),
                    backgroundColor: 'rgba(66, 153, 225, 0.6)',
                    borderColor: '#4299e1',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
    
    setupHeatmap() {
        const canvas = document.getElementById('heatmapCanvas');
        const container = canvas.parentElement;
        
        canvas.width = container.offsetWidth;
        canvas.height = container.offsetHeight;
        
        this.heatmapCtx = canvas.getContext('2d');
        this.heatmapData = [];
    }
    
    showView(viewName) {
        // Hide all views
        document.querySelectorAll('.view-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        
        // Show selected view
        document.getElementById(viewName + 'View').classList.add('active');
        
        // Update view buttons
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-view="${viewName}"]`).classList.add('active');
        
        this.currentView = viewName;
        
        // Update page title and breadcrumb
        this.updatePageTitle(viewName);
        
        // Trigger view-specific updates
        this.onViewChange(viewName);
    }
    
    updatePageTitle(viewName) {
        const titles = {
            realtime: 'Real-time Analytics',
            heatmap: 'Mouse Heatmap',
            patterns: 'Pattern Analysis',
            comparison: 'Session Comparison'
        };
        
        const breadcrumbs = {
            realtime: 'Live Session',
            heatmap: 'Heatmap View',
            patterns: 'Pattern Analysis',
            comparison: 'Comparison'
        };
        
        document.getElementById('pageTitle').textContent = titles[viewName];
        document.querySelector('.breadcrumb span:last-child').textContent = breadcrumbs[viewName];
    }
    
    onViewChange(viewName) {
        switch (viewName) {
            case 'heatmap':
                this.updateHeatmap();
                break;
            case 'patterns':
                this.updatePatternAnalysis();
                break;
            case 'comparison':
                this.updateComparisonSelectors();
                break;
        }
    }

    toggleRecording() {
        if (this.isRecording) {
            this.stopRecording();
        } else {
            this.startRecording();
        }
    }

    startRecording() {
        if (this.isRecording) return;

        const settings = {
            sampleRate: 60,
            captureVelocity: true,
            captureAcceleration: true,
            captureScrollMomentum: true,
            onStart: (sessionId) => this.onRecordingStart(sessionId),
            onStop: (sessionData) => this.onRecordingStop(sessionData),
            onDataPoint: (interaction) => this.onDataPoint(interaction),
            onError: (error) => this.onError(error)
        };

        this.collector = new HumanInteractionCollector(settings);

        if (this.collector.startRecording()) {
            this.isRecording = true;
            this.isPaused = false;
            this.recordingStartTime = Date.now();

            this.updateRecordingUI();
            this.startRealtimeUpdates();
            this.clearRealtimeData();
        }
    }

    pauseRecording() {
        if (!this.isRecording || this.isPaused) return;

        this.isPaused = true;
        this.updateRecordingUI();
    }

    stopRecording() {
        if (!this.isRecording) return;

        const sessionData = this.collector.stopRecording();
        this.isRecording = false;
        this.isPaused = false;

        this.stopRealtimeUpdates();
        this.updateRecordingUI();

        if (sessionData) {
            this.saveSession(sessionData);
            this.currentSession = sessionData;
            this.refreshSessionList();
            this.updateAllViews();
        }
    }

    onRecordingStart(sessionId) {
        console.log('Recording started:', sessionId);
    }

    onRecordingStop(sessionData) {
        console.log('Recording stopped:', sessionData);
    }

    onDataPoint(interaction) {
        if (!this.isRecording || this.isPaused) return;

        // Add to realtime data
        this.realtimeData.interactions.push(interaction);

        // Process different interaction types
        switch (interaction.type) {
            case 'mousemove':
                this.realtimeData.mousePositions.push({
                    x: interaction.data.x,
                    y: interaction.data.y,
                    timestamp: interaction.timestamp
                });

                if (interaction.data.velocity) {
                    this.realtimeData.velocities.push({
                        value: interaction.data.velocity,
                        timestamp: interaction.timestamp
                    });
                }
                break;

            case 'click':
                this.realtimeData.clickTimes.push(interaction.timestamp);
                break;

            case 'keydown':
                this.realtimeData.keyTimes.push(interaction.timestamp);
                break;
        }

        // Update heatmap if in heatmap view
        if (this.currentView === 'heatmap' && interaction.type === 'mousemove') {
            this.addHeatmapPoint(interaction.data.x, interaction.data.y);
        }

        // Limit data size for performance
        this.limitRealtimeData();
    }

    onError(error) {
        console.error('Recording error:', error);
        this.showNotification('Recording error: ' + error.message, 'error');
    }

    updateRecordingUI() {
        const recordBtn = document.getElementById('recordBtn');
        const pauseBtn = document.getElementById('pauseBtn');
        const stopBtn = document.getElementById('stopBtn');
        const statusDot = document.querySelector('.status-dot');
        const statusText = document.querySelector('.status-text');

        if (this.isRecording) {
            recordBtn.textContent = '⏹️ Stop Recording';
            recordBtn.classList.remove('record-btn');
            pauseBtn.disabled = false;
            stopBtn.disabled = false;

            if (this.isPaused) {
                statusDot.className = 'status-dot paused';
                statusText.textContent = 'Paused';
            } else {
                statusDot.className = 'status-dot recording';
                statusText.textContent = 'Recording';
            }
        } else {
            recordBtn.textContent = '🔴 Start Recording';
            recordBtn.classList.add('record-btn');
            pauseBtn.disabled = true;
            stopBtn.disabled = true;
            statusDot.className = 'status-dot';
            statusText.textContent = 'Ready';
        }
    }

    startRealtimeUpdates() {
        // Update metrics every second
        this.metricsInterval = setInterval(() => {
            this.updateMetrics();
        }, 1000);

        // Update charts every 500ms
        this.chartsInterval = setInterval(() => {
            if (!this.chartsPaused) {
                this.updateRealtimeCharts();
            }
        }, 500);
    }

    stopRealtimeUpdates() {
        if (this.metricsInterval) {
            clearInterval(this.metricsInterval);
            this.metricsInterval = null;
        }

        if (this.chartsInterval) {
            clearInterval(this.chartsInterval);
            this.chartsInterval = null;
        }
    }

    updateMetrics() {
        if (!this.isRecording || this.isPaused) return;

        const now = Date.now();
        const duration = (now - this.recordingStartTime) / 1000;

        // Calculate interaction rate (interactions per second)
        const recentInteractions = this.realtimeData.interactions.filter(
            i => now - i.timestamp < 1000
        );
        this.metrics.interactionRate = recentInteractions.length;

        // Calculate average mouse velocity
        const recentVelocities = this.realtimeData.velocities.filter(
            v => now - v.timestamp < 1000
        );
        this.metrics.mouseVelocity = recentVelocities.length > 0 ?
            recentVelocities.reduce((sum, v) => sum + v.value, 0) / recentVelocities.length : 0;

        // Calculate click frequency (clicks per minute)
        const recentClicks = this.realtimeData.clickTimes.filter(
            t => now - t < 60000
        );
        this.metrics.clickFrequency = recentClicks.length;

        // Update session duration
        this.metrics.sessionDuration = duration;

        // Update UI
        this.updateMetricsDisplay();
    }

    updateMetricsDisplay() {
        document.getElementById('interactionRate').textContent = this.metrics.interactionRate;
        document.getElementById('mouseVelocity').textContent =
            Math.round(this.metrics.mouseVelocity) + ' px/s';
        document.getElementById('clickFrequency').textContent =
            this.metrics.clickFrequency + '/min';
        document.getElementById('sessionDuration').textContent =
            this.formatTime(this.metrics.sessionDuration * 1000);

        // Update recording timer
        document.getElementById('recordingTimer').textContent =
            this.formatTime(this.metrics.sessionDuration * 1000);
    }

    updateRealtimeCharts() {
        if (!this.isRecording) return;

        const now = Date.now();

        // Update timeline chart
        const timelineChart = this.charts.timeline;
        timelineChart.data.labels.push(new Date(now));
        timelineChart.data.datasets[0].data.push(this.metrics.interactionRate);

        // Keep only last 60 data points
        if (timelineChart.data.labels.length > 60) {
            timelineChart.data.labels.shift();
            timelineChart.data.datasets[0].data.shift();
        }

        timelineChart.update('none');

        // Update mouse chart
        const mouseChart = this.charts.mouse;
        const recentMouse = this.realtimeData.mousePositions.slice(-100);
        mouseChart.data.datasets[0].data = recentMouse.map(pos => ({
            x: pos.x,
            y: pos.y
        }));
        mouseChart.update('none');

        // Update distribution chart
        this.updateDistributionChart('type');

        // Update performance chart
        const performanceChart = this.charts.performance;
        performanceChart.data.labels.push(new Date(now));
        performanceChart.data.datasets[0].data.push(this.metrics.mouseVelocity);
        performanceChart.data.datasets[1].data.push(this.metrics.clickFrequency);

        // Keep only last 60 data points
        if (performanceChart.data.labels.length > 60) {
            performanceChart.data.labels.shift();
            performanceChart.data.datasets[0].data.shift();
            performanceChart.data.datasets[1].data.shift();
        }

        performanceChart.update('none');
    }

    clearRealtimeData() {
        this.realtimeData = {
            interactions: [],
            mousePositions: [],
            velocities: [],
            clickTimes: [],
            keyTimes: []
        };
    }

    limitRealtimeData() {
        const maxSize = 10000;

        if (this.realtimeData.interactions.length > maxSize) {
            this.realtimeData.interactions = this.realtimeData.interactions.slice(-maxSize);
        }

        if (this.realtimeData.mousePositions.length > maxSize) {
            this.realtimeData.mousePositions = this.realtimeData.mousePositions.slice(-maxSize);
        }

        if (this.realtimeData.velocities.length > maxSize) {
            this.realtimeData.velocities = this.realtimeData.velocities.slice(-maxSize);
        }

        const now = Date.now();
        const maxAge = 300000; // 5 minutes

        this.realtimeData.clickTimes = this.realtimeData.clickTimes.filter(t => now - t < maxAge);
        this.realtimeData.keyTimes = this.realtimeData.keyTimes.filter(t => now - t < maxAge);
    }

    updateDistributionChart(type) {
        const chart = this.charts.distribution;
        let data;

        if (this.isRecording) {
            // Use realtime data
            const interactions = this.realtimeData.interactions;
            data = this.calculateDistribution(interactions, type);
        } else if (this.currentSession) {
            // Use current session data
            data = this.calculateDistribution(this.currentSession.interactions, type);
        } else {
            data = [0, 0, 0, 0];
        }

        chart.data.datasets[0].data = data;
        chart.update('none');
    }

    calculateDistribution(interactions, type) {
        switch (type) {
            case 'type':
                const mouseMoves = interactions.filter(i => i.type === 'mousemove').length;
                const clicks = interactions.filter(i => i.type === 'click').length;
                const keys = interactions.filter(i => i.type === 'keydown').length;
                const scrolls = interactions.filter(i => i.type === 'wheel').length;
                return [mouseMoves, clicks, keys, scrolls];

            case 'time':
                // Distribution by hour of day
                const hours = new Array(24).fill(0);
                interactions.forEach(i => {
                    const hour = new Date(i.timestamp).getHours();
                    hours[hour]++;
                });
                return hours.slice(0, 4); // Take first 4 hours for pie chart

            case 'velocity':
                // Distribution by velocity ranges
                const ranges = [0, 0, 0, 0]; // slow, medium, fast, very fast
                interactions.forEach(i => {
                    if (i.data.velocity) {
                        if (i.data.velocity < 100) ranges[0]++;
                        else if (i.data.velocity < 500) ranges[1]++;
                        else if (i.data.velocity < 1000) ranges[2]++;
                        else ranges[3]++;
                    }
                });
                return ranges;

            default:
                return [0, 0, 0, 0];
        }
    }

    clearChartData() {
        Object.values(this.charts).forEach(chart => {
            if (chart.data.labels) {
                chart.data.labels = [];
            }
            chart.data.datasets.forEach(dataset => {
                dataset.data = [];
            });
            chart.update();
        });
    }

    resetMouseChart() {
        const chart = this.charts.mouse;
        chart.data.datasets[0].data = [];
        chart.update();
    }

    // Heatmap functionality
    updateHeatmap() {
        if (!this.currentSession) return;

        this.resetHeatmap();

        const mouseEvents = this.currentSession.interactions.filter(i => i.type === 'mousemove');
        mouseEvents.forEach(event => {
            this.addHeatmapPoint(event.data.x, event.data.y);
        });
    }

    addHeatmapPoint(x, y) {
        this.heatmapData.push({ x, y });
        this.renderHeatmap();
    }

    renderHeatmap() {
        const canvas = document.getElementById('heatmapCanvas');
        const ctx = this.heatmapCtx;
        const intensity = parseFloat(document.getElementById('heatmapIntensity').value);
        const radius = parseInt(document.getElementById('heatmapRadius').value);

        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Create heatmap
        this.heatmapData.forEach(point => {
            const gradient = ctx.createRadialGradient(point.x, point.y, 0, point.x, point.y, radius);
            gradient.addColorStop(0, `rgba(255, 0, 0, ${intensity * 0.8})`);
            gradient.addColorStop(0.5, `rgba(255, 255, 0, ${intensity * 0.4})`);
            gradient.addColorStop(1, 'rgba(255, 255, 0, 0)');

            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(point.x, point.y, radius, 0, Math.PI * 2);
            ctx.fill();
        });
    }

    updateHeatmapSettings() {
        this.renderHeatmap();
    }

    resetHeatmap() {
        this.heatmapData = [];
        const ctx = this.heatmapCtx;
        const canvas = document.getElementById('heatmapCanvas');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
    }

    // Pattern analysis
    updatePatternAnalysis() {
        if (!this.currentSession) return;

        const interactions = this.currentSession.interactions;

        // Analyze movement patterns
        this.analyzeMovementPatterns(interactions);

        // Analyze click patterns
        this.analyzeClickPatterns(interactions);

        // Update temporal chart
        this.updateTemporalChart(interactions);

        // Generate behavioral insights
        this.generateBehavioralInsights(interactions);
    }

    analyzeMovementPatterns(interactions) {
        const mouseEvents = interactions.filter(i => i.type === 'mousemove');
        const patterns = { linear: 0, circular: 0, erratic: 0 };

        // Simple pattern detection (this could be much more sophisticated)
        for (let i = 2; i < mouseEvents.length; i++) {
            const p1 = mouseEvents[i - 2];
            const p2 = mouseEvents[i - 1];
            const p3 = mouseEvents[i];

            const angle1 = Math.atan2(p2.data.y - p1.data.y, p2.data.x - p1.data.x);
            const angle2 = Math.atan2(p3.data.y - p2.data.y, p3.data.x - p2.data.x);
            const angleDiff = Math.abs(angle2 - angle1);

            if (angleDiff < 0.1) patterns.linear++;
            else if (angleDiff > 2.8) patterns.circular++;
            else patterns.erratic++;
        }

        const total = patterns.linear + patterns.circular + patterns.erratic;
        if (total > 0) {
            const container = document.getElementById('movementPatterns');
            container.innerHTML = `
                <div class="pattern-item">
                    <span class="pattern-name">Linear Movement</span>
                    <span class="pattern-frequency">${Math.round(patterns.linear / total * 100)}%</span>
                </div>
                <div class="pattern-item">
                    <span class="pattern-name">Circular Movement</span>
                    <span class="pattern-frequency">${Math.round(patterns.circular / total * 100)}%</span>
                </div>
                <div class="pattern-item">
                    <span class="pattern-name">Erratic Movement</span>
                    <span class="pattern-frequency">${Math.round(patterns.erratic / total * 100)}%</span>
                </div>
            `;
        }
    }

    analyzeClickPatterns(interactions) {
        const clickEvents = interactions.filter(i => i.type === 'click');
        const patterns = { single: 0, double: 0, drag: 0 };

        // Analyze click patterns
        for (let i = 0; i < clickEvents.length; i++) {
            const current = clickEvents[i];
            const next = clickEvents[i + 1];

            if (next && next.timestamp - current.timestamp < 500) {
                patterns.double++;
                i++; // Skip next click as it's part of double click
            } else {
                patterns.single++;
            }
        }

        // Count drag operations (simplified)
        const mouseEvents = interactions.filter(i => i.type === 'mousemove');
        patterns.drag = mouseEvents.filter(e => e.data.buttons && e.data.buttons > 0).length;

        const total = patterns.single + patterns.double + patterns.drag;
        if (total > 0) {
            const container = document.getElementById('clickPatterns');
            container.innerHTML = `
                <div class="pattern-item">
                    <span class="pattern-name">Single Clicks</span>
                    <span class="pattern-frequency">${Math.round(patterns.single / total * 100)}%</span>
                </div>
                <div class="pattern-item">
                    <span class="pattern-name">Double Clicks</span>
                    <span class="pattern-frequency">${Math.round(patterns.double / total * 100)}%</span>
                </div>
                <div class="pattern-item">
                    <span class="pattern-name">Drag Operations</span>
                    <span class="pattern-frequency">${Math.round(patterns.drag / total * 100)}%</span>
                </div>
            `;
        }
    }

    updateTemporalChart(interactions) {
        const hourlyData = new Array(24).fill(0);

        interactions.forEach(interaction => {
            const hour = new Date(interaction.timestamp).getHours();
            hourlyData[hour]++;
        });

        this.charts.temporal.data.datasets[0].data = hourlyData;
        this.charts.temporal.update();
    }

    generateBehavioralInsights(interactions) {
        const insights = [];

        // Analyze precision
        const clickEvents = interactions.filter(i => i.type === 'click');
        const mouseEvents = interactions.filter(i => i.type === 'mousemove');

        if (clickEvents.length > 0 && mouseEvents.length > 0) {
            const avgVelocity = mouseEvents
                .filter(e => e.data.velocity)
                .reduce((sum, e) => sum + e.data.velocity, 0) / mouseEvents.length;

            if (avgVelocity < 200) {
                insights.push({
                    icon: '🎯',
                    text: 'High precision in target selection'
                });
            } else if (avgVelocity > 800) {
                insights.push({
                    icon: '⚡',
                    text: 'Fast interaction pace detected'
                });
            }
        }

        // Analyze repetitive patterns
        if (mouseEvents.length > 100) {
            insights.push({
                icon: '🔄',
                text: 'Repetitive movement patterns detected'
            });
        }

        // Update UI
        const container = document.getElementById('behavioralInsights');
        container.innerHTML = insights.map(insight => `
            <div class="insight-item">
                <span class="insight-icon">${insight.icon}</span>
                <span class="insight-text">${insight.text}</span>
            </div>
        `).join('');
    }

    // Session management
    saveSession(sessionData) {
        UIUtils.saveSession(sessionData);
        this.sessions.push({
            id: sessionData.session.id,
            timestamp: sessionData.session.startTime,
            duration: sessionData.session.duration,
            interactionCount: sessionData.interactions.length
        });
    }

    loadSessions() {
        this.sessions = UIUtils.getSavedSessions();
    }

    refreshSessionList() {
        this.loadSessions();

        const container = document.getElementById('sessionList');

        if (this.sessions.length === 0) {
            container.innerHTML = '<div class="empty-sessions">No sessions available</div>';
            return;
        }

        container.innerHTML = this.sessions.map(session => `
            <div class="session-item" data-session-id="${session.id}">
                Session ${session.id.substring(0, 8)} (${session.interactionCount} interactions)
            </div>
        `).join('');

        // Bind click events
        container.querySelectorAll('.session-item').forEach(item => {
            item.addEventListener('click', () => {
                const sessionId = item.dataset.sessionId;
                this.loadSession(sessionId);

                // Update selection
                container.querySelectorAll('.session-item').forEach(i => i.classList.remove('active'));
                item.classList.add('active');
            });
        });
    }

    loadSession(sessionId) {
        const sessionData = UIUtils.loadSession(sessionId);
        if (sessionData) {
            this.currentSession = sessionData;
            this.updateAllViews();
        }
    }

    updateAllViews() {
        // Update distribution chart
        this.updateDistributionChart('type');

        // Update view-specific content
        switch (this.currentView) {
            case 'heatmap':
                this.updateHeatmap();
                break;
            case 'patterns':
                this.updatePatternAnalysis();
                break;
            case 'comparison':
                this.updateComparisonSelectors();
                break;
        }
    }

    // Comparison functionality
    updateComparisonSelectors() {
        const session1Select = document.getElementById('session1Select');
        const session2Select = document.getElementById('session2Select');

        const options = this.sessions.map(session =>
            `<option value="${session.id}">Session ${session.id.substring(0, 8)} (${UIUtils.formatTimestamp(session.timestamp)})</option>`
        ).join('');

        session1Select.innerHTML = '<option value="">Select Session 1</option>' + options;
        session2Select.innerHTML = '<option value="">Select Session 2</option>' + options;
    }

    compareSelectedSessions() {
        const session1Id = document.getElementById('session1Select').value;
        const session2Id = document.getElementById('session2Select').value;

        if (!session1Id || !session2Id) {
            this.showNotification('Please select two sessions to compare', 'warning');
            return;
        }

        const session1 = UIUtils.loadSession(session1Id);
        const session2 = UIUtils.loadSession(session2Id);

        if (!session1 || !session2) {
            this.showNotification('Failed to load session data', 'error');
            return;
        }

        this.renderComparison(session1, session2);
    }

    renderComparison(session1, session2) {
        const container = document.getElementById('comparisonResults');

        // Calculate metrics for both sessions
        const metrics1 = this.calculateSessionMetrics(session1);
        const metrics2 = this.calculateSessionMetrics(session2);

        container.innerHTML = `
            <div class="comparison-grid">
                <div class="comparison-section">
                    <h4>Session Metrics</h4>
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>Metric</th>
                                <th>Session 1</th>
                                <th>Session 2</th>
                                <th>Difference</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Duration</td>
                                <td>${this.formatTime(metrics1.duration)}</td>
                                <td>${this.formatTime(metrics2.duration)}</td>
                                <td>${this.formatDifference(metrics1.duration, metrics2.duration, 'time')}</td>
                            </tr>
                            <tr>
                                <td>Total Interactions</td>
                                <td>${metrics1.totalInteractions}</td>
                                <td>${metrics2.totalInteractions}</td>
                                <td>${this.formatDifference(metrics1.totalInteractions, metrics2.totalInteractions, 'count')}</td>
                            </tr>
                            <tr>
                                <td>Avg Mouse Velocity</td>
                                <td>${Math.round(metrics1.avgVelocity)} px/s</td>
                                <td>${Math.round(metrics2.avgVelocity)} px/s</td>
                                <td>${this.formatDifference(metrics1.avgVelocity, metrics2.avgVelocity, 'velocity')}</td>
                            </tr>
                            <tr>
                                <td>Click Rate</td>
                                <td>${metrics1.clickRate.toFixed(1)}/min</td>
                                <td>${metrics2.clickRate.toFixed(1)}/min</td>
                                <td>${this.formatDifference(metrics1.clickRate, metrics2.clickRate, 'rate')}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    calculateSessionMetrics(sessionData) {
        const interactions = sessionData.interactions;
        const duration = sessionData.session.duration;

        const mouseEvents = interactions.filter(i => i.type === 'mousemove');
        const clickEvents = interactions.filter(i => i.type === 'click');

        const avgVelocity = mouseEvents
            .filter(e => e.data.velocity)
            .reduce((sum, e, _, arr) => sum + e.data.velocity / arr.length, 0);

        const clickRate = duration > 0 ? (clickEvents.length / (duration / 1000)) * 60 : 0;

        return {
            duration,
            totalInteractions: interactions.length,
            avgVelocity,
            clickRate
        };
    }

    formatDifference(val1, val2, type) {
        const diff = val1 - val2;
        const percent = val2 !== 0 ? ((diff / val2) * 100) : 0;

        let formattedDiff;
        switch (type) {
            case 'time':
                formattedDiff = this.formatTime(Math.abs(diff));
                break;
            case 'velocity':
                formattedDiff = Math.round(Math.abs(diff)) + ' px/s';
                break;
            default:
                formattedDiff = Math.abs(diff).toFixed(1);
        }

        const sign = diff >= 0 ? '+' : '-';
        const className = diff >= 0 ? 'positive' : 'negative';

        return `<span class="metric-change ${className}">${sign}${formattedDiff} (${sign}${Math.abs(percent).toFixed(1)}%)</span>`;
    }

    // Export functionality
    exportCurrentSession() {
        if (!this.currentSession) {
            this.showNotification('No session to export', 'warning');
            return;
        }

        const filename = `dashboard-session-${this.currentSession.session.id.substring(0, 8)}.json`;
        const content = JSON.stringify(this.currentSession, null, 2);
        UIUtils.downloadFile(content, filename);
        this.showNotification('Session exported successfully', 'success');
    }

    exportAllSessions() {
        if (this.sessions.length === 0) {
            this.showNotification('No sessions to export', 'warning');
            return;
        }

        const allSessionsData = this.sessions.map(session => UIUtils.loadSession(session.id)).filter(Boolean);
        const exportData = {
            exportTimestamp: new Date().toISOString(),
            totalSessions: allSessionsData.length,
            sessions: allSessionsData
        };

        const filename = `dashboard-all-sessions-${new Date().toISOString().split('T')[0]}.json`;
        UIUtils.downloadFile(JSON.stringify(exportData, null, 2), filename);
        this.showNotification(`Exported ${allSessionsData.length} sessions`, 'success');
    }

    exportMetricsData() {
        if (!this.currentSession) {
            this.showNotification('No session data to export', 'warning');
            return;
        }

        const metrics = this.calculateSessionMetrics(this.currentSession);
        const exportData = {
            sessionId: this.currentSession.session.id,
            exportTimestamp: new Date().toISOString(),
            metrics: metrics,
            chartData: {
                timeline: this.charts.timeline.data,
                distribution: this.charts.distribution.data,
                performance: this.charts.performance.data
            }
        };

        const filename = `dashboard-metrics-${this.currentSession.session.id.substring(0, 8)}.json`;
        UIUtils.downloadFile(JSON.stringify(exportData, null, 2), filename);
        this.showNotification('Metrics exported successfully', 'success');
    }

    // Time range functionality
    updateTimeRange(range) {
        // This would filter data based on the selected time range
        // For now, we'll just log the selection
        console.log('Time range updated:', range);
    }

    // Utility functions
    formatTime(milliseconds) {
        const totalSeconds = Math.floor(milliseconds / 1000);
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;

        if (hours > 0) {
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        } else {
            return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }

    showNotification(message, type = 'info') {
        // Simple notification system
        console.log(`[${type.toUpperCase()}] ${message}`);

        // You could implement a toast notification system here
        // For now, we'll use console logging
    }
}

// Add comparison table styles
const style = document.createElement('style');
style.textContent = `
    .comparison-grid {
        width: 100%;
    }

    .comparison-section {
        margin-bottom: 24px;
    }

    .comparison-section h4 {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: #2d3748;
    }

    .comparison-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .comparison-table th,
    .comparison-table td {
        padding: 12px 16px;
        text-align: left;
        border-bottom: 1px solid #e2e8f0;
    }

    .comparison-table th {
        background: #f8fafc;
        font-weight: 600;
        color: #4a5568;
        font-size: 14px;
    }

    .comparison-table td {
        font-size: 14px;
        color: #2d3748;
    }

    .comparison-table tr:last-child td {
        border-bottom: none;
    }
`;
document.head.appendChild(style);

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.analyticsDashboard = new AnalyticsDashboardController();
});

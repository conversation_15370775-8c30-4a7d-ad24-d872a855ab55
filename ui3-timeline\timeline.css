/**
 * Timeline-Based Recorder Styles
 */

body {
    margin: 0;
    padding: 0;
    background: #1a1a1a;
    color: #ffffff;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    overflow: hidden;
}

.timeline-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.timeline-header {
    background: #2d2d2d;
    border-bottom: 1px solid #404040;
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.app-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.title-icon {
    font-size: 20px;
}

.recording-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    font-size: 14px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #95a5a6;
    transition: all 0.3s ease;
}

.recording-status.recording .status-dot {
    background: #e74c3c;
    animation: pulse 1.5s infinite;
}

.recording-status.paused .status-dot {
    background: #f39c12;
}

.header-center {
    display: flex;
    align-items: center;
}

.timeline-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.05);
    padding: 8px;
    border-radius: 8px;
}

.timeline-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.2s ease;
}

.timeline-btn:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.timeline-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.timeline-btn.active {
    background: #3498db;
}

.timeline-divider {
    width: 1px;
    height: 24px;
    background: rgba(255, 255, 255, 0.2);
    margin: 0 8px;
}

.header-right {
    display: flex;
    align-items: center;
}

.session-info {
    text-align: right;
}

.current-time {
    font-family: 'Courier New', monospace;
    font-size: 18px;
    font-weight: bold;
    color: #3498db;
}

.total-interactions {
    font-size: 12px;
    color: #bbb;
}

/* Main Timeline */
.timeline-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Timeline Ruler */
.timeline-ruler {
    background: #333;
    border-bottom: 1px solid #404040;
    height: 40px;
    position: relative;
    flex-shrink: 0;
}

.ruler-track {
    height: 100%;
    position: relative;
    overflow: hidden;
}

.playhead {
    position: absolute;
    top: 0;
    left: 0;
    width: 2px;
    height: 100%;
    background: #e74c3c;
    z-index: 10;
    transition: left 0.1s ease;
}

.ruler-markers {
    height: 100%;
    position: relative;
}

.ruler-marker {
    position: absolute;
    top: 0;
    width: 1px;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
}

.ruler-marker.major {
    background: rgba(255, 255, 255, 0.4);
}

.ruler-label {
    position: absolute;
    top: 2px;
    font-size: 10px;
    color: #bbb;
    font-family: monospace;
}

/* Timeline Tracks */
.timeline-tracks {
    flex: 1;
    overflow-y: auto;
    background: #1a1a1a;
}

.track {
    border-bottom: 1px solid #333;
    min-height: 80px;
    display: flex;
}

.track-header {
    width: 200px;
    background: #2d2d2d;
    border-right: 1px solid #404040;
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.track-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
}

.track-icon {
    font-size: 16px;
}

.track-controls {
    display: flex;
    gap: 4px;
}

.track-toggle,
.track-solo {
    width: 24px;
    height: 24px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    color: white;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.track-toggle:hover,
.track-solo:hover {
    background: rgba(255, 255, 255, 0.2);
}

.track-toggle.active {
    background: #3498db;
}

.track-solo.active {
    background: #f39c12;
}

.track-content {
    flex: 1;
    position: relative;
    background: #1a1a1a;
    overflow: hidden;
}

.track-canvas {
    width: 100%;
    height: 100%;
    display: block;
}

/* Event Markers */
.event-markers {
    height: 100%;
    position: relative;
}

.event-marker {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
}

.event-marker:hover {
    transform: translateY(-50%) scale(1.5);
}

.event-marker.click {
    background: #e74c3c;
}

.event-marker.key {
    background: #3498db;
}

.event-marker.scroll {
    background: #2ecc71;
}

.event-marker.selected {
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.5);
}

/* Bottom Panel */
.bottom-panel {
    height: 300px;
    background: #2d2d2d;
    border-top: 1px solid #404040;
    display: flex;
    flex-shrink: 0;
}

.session-panel,
.properties-panel,
.analysis-panel {
    flex: 1;
    border-right: 1px solid #404040;
    display: flex;
    flex-direction: column;
}

.analysis-panel {
    border-right: none;
}

.panel-header {
    padding: 12px 16px;
    border-bottom: 1px solid #404040;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #333;
}

.panel-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
}

.panel-controls {
    display: flex;
    gap: 8px;
}

.session-list,
.properties-content,
.analysis-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

/* Session List */
.session-item {
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.session-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.session-item.active {
    background: rgba(52, 152, 219, 0.2);
    border: 1px solid #3498db;
}

.session-title {
    font-weight: 600;
    margin-bottom: 4px;
}

.session-meta {
    font-size: 12px;
    color: #bbb;
}

/* Properties Panel */
.property-item {
    margin-bottom: 12px;
}

.property-label {
    font-size: 12px;
    color: #bbb;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.property-value {
    font-family: monospace;
    background: rgba(255, 255, 255, 0.05);
    padding: 6px 8px;
    border-radius: 4px;
    font-size: 13px;
}

/* Analysis Panel */
.analysis-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.metric-item {
    text-align: center;
}

.metric-label {
    display: block;
    font-size: 12px;
    color: #bbb;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.metric-value {
    display: block;
    font-size: 18px;
    font-weight: bold;
    color: #3498db;
}

/* Modal Styles */
.modal-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal-overlay.show {
    display: flex;
}

.export-modal {
    background: #2d2d2d;
    border-radius: 8px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    max-width: 500px;
    width: 90vw;
    max-height: 80vh;
    overflow: hidden;
}

.modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #404040;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #bbb;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.modal-content {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 16px 24px 24px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    border-top: 1px solid #404040;
}

/* Export Options */
.export-options {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.option-label {
    font-size: 14px;
    font-weight: 500;
    color: #ddd;
}

.time-range-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.time-input {
    flex: 1;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid #404040;
    color: white;
}

.range-separator {
    color: #bbb;
    font-size: 14px;
}

.track-checkboxes {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.track-checkboxes label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    cursor: pointer;
}

/* Utility Classes */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-style: italic;
}

.no-selection {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-style: italic;
}

/* Animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .bottom-panel {
        height: 250px;
    }
    
    .analysis-metrics {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .timeline-header {
        flex-direction: column;
        gap: 12px;
        padding: 16px;
    }
    
    .bottom-panel {
        flex-direction: column;
        height: auto;
        max-height: 50vh;
    }
    
    .session-panel,
    .properties-panel,
    .analysis-panel {
        border-right: none;
        border-bottom: 1px solid #404040;
        min-height: 150px;
    }
}

/**
 * Floating Panel Controller
 * Minimalist draggable interface for human interaction recording
 */

class FloatingPanelController {
    constructor() {
        this.collector = null;
        this.isRecording = false;
        this.isPaused = false;
        this.isMinimized = false;
        this.recordingStartTime = null;
        this.timerInterval = null;
        this.metricsInterval = null;
        this.currentSession = null;
        
        // Drag functionality
        this.isDragging = false;
        this.dragOffset = { x: 0, y: 0 };
        
        // Settings
        this.settings = {
            sampleRate: 60,
            opacity: 95,
            alwaysOnTop: true,
            autoMinimize: false,
            captureVelocity: true,
            captureAcceleration: true,
            captureScrollMomentum: true
        };
        
        this.initializeUI();
        this.bindEvents();
        this.loadSettings();
        this.setupDragAndDrop();
    }
    
    initializeUI() {
        // Load saved position
        const savedPosition = UIUtils.loadUIState('floating-position');
        if (savedPosition) {
            const panel = document.getElementById('floatingPanel');
            panel.style.left = savedPosition.x + 'px';
            panel.style.top = savedPosition.y + 'px';
            panel.style.right = 'auto';
        }
        
        // Apply initial settings
        this.applySettings();
    }
    
    bindEvents() {
        // Recording controls
        document.getElementById('recordBtn').addEventListener('click', () => this.toggleRecording());
        document.getElementById('pauseBtn').addEventListener('click', () => this.pauseRecording());
        document.getElementById('stopBtn').addEventListener('click', () => this.stopRecording());
        
        // Panel controls
        document.getElementById('minimizeBtn').addEventListener('click', () => this.minimizePanel());
        document.getElementById('restoreBtn').addEventListener('click', () => this.restorePanel());
        document.getElementById('settingsBtn').addEventListener('click', () => this.showSettings());
        
        // Action buttons
        document.getElementById('exportBtn').addEventListener('click', () => this.exportLastSession());
        document.getElementById('historyBtn').addEventListener('click', () => this.showHistory());
        document.getElementById('helpBtn').addEventListener('click', () => this.showHelp());
        
        // Modal controls
        this.bindModalEvents();
        
        // Keyboard shortcuts
        this.setupKeyboardShortcuts();
        
        // Settings controls
        this.bindSettingsEvents();
    }
    
    bindModalEvents() {
        // Settings modal
        document.getElementById('closeSettingsBtn').addEventListener('click', () => this.hideSettings());
        document.getElementById('saveSettingsBtn').addEventListener('click', () => this.saveSettings());
        document.getElementById('resetSettingsBtn').addEventListener('click', () => this.resetSettings());
        
        // History modal
        document.getElementById('closeHistoryBtn').addEventListener('click', () => this.hideHistory());
        document.getElementById('clearHistoryBtn').addEventListener('click', () => this.clearHistory());
        document.getElementById('exportAllBtn').addEventListener('click', () => this.exportAllSessions());
        
        // Help modal
        document.getElementById('closeHelpBtn').addEventListener('click', () => this.hideHelp());
        
        // Click outside to close
        document.querySelectorAll('.modal-overlay').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.classList.remove('show');
                }
            });
        });
    }
    
    bindSettingsEvents() {
        // Sample rate slider
        const sampleRateSlider = document.getElementById('sampleRateSlider');
        const sampleRateValue = document.getElementById('sampleRateValue');
        sampleRateSlider.addEventListener('input', (e) => {
            sampleRateValue.textContent = e.target.value;
        });
        
        // Opacity slider
        const opacitySlider = document.getElementById('opacitySlider');
        const opacityValue = document.getElementById('opacityValue');
        opacitySlider.addEventListener('input', (e) => {
            opacityValue.textContent = e.target.value + '%';
            this.updatePanelOpacity(e.target.value);
        });
    }
    
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey) {
                switch (e.key.toLowerCase()) {
                    case 'r':
                        e.preventDefault();
                        this.toggleRecording();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.pauseRecording();
                        break;
                    case 's':
                        e.preventDefault();
                        this.stopRecording();
                        break;
                    case 'm':
                        e.preventDefault();
                        this.toggleMinimize();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.exportLastSession();
                        break;
                    case ',':
                        e.preventDefault();
                        this.showSettings();
                        break;
                }
            }
            
            if (e.key === 'Escape') {
                // Close any open modals
                document.querySelectorAll('.modal-overlay.show').forEach(modal => {
                    modal.classList.remove('show');
                });
            }
        });
    }
    
    setupDragAndDrop() {
        const header = document.getElementById('panelHeader');
        
        header.addEventListener('mousedown', (e) => {
            this.isDragging = true;
            const panel = document.getElementById('floatingPanel');
            const rect = panel.getBoundingClientRect();
            
            this.dragOffset.x = e.clientX - rect.left;
            this.dragOffset.y = e.clientY - rect.top;
            
            document.addEventListener('mousemove', this.handleDrag);
            document.addEventListener('mouseup', this.handleDragEnd);
            
            e.preventDefault();
        });
    }
    
    handleDrag = (e) => {
        if (!this.isDragging) return;
        
        const panel = document.getElementById('floatingPanel');
        const newX = e.clientX - this.dragOffset.x;
        const newY = e.clientY - this.dragOffset.y;
        
        // Keep panel within viewport
        const maxX = window.innerWidth - panel.offsetWidth;
        const maxY = window.innerHeight - panel.offsetHeight;
        
        const constrainedX = Math.max(0, Math.min(newX, maxX));
        const constrainedY = Math.max(0, Math.min(newY, maxY));
        
        panel.style.left = constrainedX + 'px';
        panel.style.top = constrainedY + 'px';
        panel.style.right = 'auto';
    }
    
    handleDragEnd = () => {
        if (this.isDragging) {
            this.isDragging = false;
            
            // Save position
            const panel = document.getElementById('floatingPanel');
            const position = {
                x: parseInt(panel.style.left),
                y: parseInt(panel.style.top)
            };
            UIUtils.saveUIState('floating-position', position);
        }
        
        document.removeEventListener('mousemove', this.handleDrag);
        document.removeEventListener('mouseup', this.handleDragEnd);
    }
    
    toggleRecording() {
        if (this.isRecording) {
            this.stopRecording();
        } else {
            this.startRecording();
        }
    }
    
    startRecording() {
        if (this.isRecording) return;
        
        const settings = {
            sampleRate: this.settings.sampleRate,
            captureVelocity: this.settings.captureVelocity,
            captureAcceleration: this.settings.captureAcceleration,
            captureScrollMomentum: this.settings.captureScrollMomentum,
            onStart: (sessionId) => this.onRecordingStart(sessionId),
            onStop: (sessionData) => this.onRecordingStop(sessionData),
            onDataPoint: (interaction) => this.onDataPoint(interaction),
            onError: (error) => this.onError(error)
        };
        
        this.collector = new HumanInteractionCollector(settings);
        
        if (this.collector.startRecording()) {
            this.isRecording = true;
            this.isPaused = false;
            this.recordingStartTime = Date.now();
            this.updateUI();
            this.startTimer();
            this.startMetricsUpdate();
            
            this.showToast('Recording started', 'success');
            
            // Auto-minimize if enabled
            if (this.settings.autoMinimize) {
                setTimeout(() => this.minimizePanel(), 1000);
            }
        } else {
            this.showToast('Failed to start recording', 'error');
        }
    }
    
    pauseRecording() {
        if (!this.isRecording || this.isPaused) return;
        
        this.isPaused = true;
        this.updateUI();
        this.showToast('Recording paused', 'warning');
    }
    
    stopRecording() {
        if (!this.isRecording) return;
        
        const sessionData = this.collector.stopRecording();
        this.isRecording = false;
        this.isPaused = false;
        this.currentSession = sessionData;
        
        this.stopTimer();
        this.stopMetricsUpdate();
        this.updateUI();
        
        if (sessionData) {
            UIUtils.saveSession(sessionData);
            this.showToast(`Recording stopped. ${sessionData.interactions.length} interactions captured.`, 'success');
        }
    }
    
    onRecordingStart(sessionId) {
        console.log('Recording started:', sessionId);
    }
    
    onRecordingStop(sessionData) {
        console.log('Recording stopped:', sessionData);
    }
    
    onDataPoint(interaction) {
        // Update metrics in real-time
        this.updateMetrics();
    }
    
    onError(error) {
        console.error('Recording error:', error);
        this.showToast('Recording error: ' + error.message, 'error');
    }

    updateUI() {
        const recordBtn = document.getElementById('recordBtn');
        const pauseBtn = document.getElementById('pauseBtn');
        const stopBtn = document.getElementById('stopBtn');
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = statusIndicator.querySelector('.status-text');

        if (this.isRecording) {
            recordBtn.style.display = 'none';
            pauseBtn.disabled = false;
            stopBtn.disabled = false;

            if (this.isPaused) {
                statusIndicator.className = 'status-indicator-large status-paused';
                statusText.textContent = 'Paused';
            } else {
                statusIndicator.className = 'status-indicator-large status-recording';
                statusText.textContent = 'Recording';
            }
        } else {
            recordBtn.style.display = 'flex';
            pauseBtn.disabled = true;
            stopBtn.disabled = true;
            statusIndicator.className = 'status-indicator-large';
            statusText.textContent = 'Ready';
        }

        // Update minimized panel status
        const minimizedStatus = document.getElementById('minimizedStatus');
        if (this.isRecording) {
            minimizedStatus.className = this.isPaused ? 'minimized-status status-paused' : 'minimized-status status-recording';
        } else {
            minimizedStatus.className = 'minimized-status';
        }
    }

    startTimer() {
        this.timerInterval = setInterval(() => {
            if (!this.isPaused) {
                const elapsed = Date.now() - this.recordingStartTime;
                const formatted = this.formatTime(elapsed);
                document.getElementById('recordingTimer').textContent = formatted;
            }
        }, 1000);
    }

    stopTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
        document.getElementById('recordingTimer').textContent = '00:00';
    }

    formatTime(milliseconds) {
        const totalSeconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    startMetricsUpdate() {
        this.metricsInterval = setInterval(() => {
            this.updateMetrics();
        }, 1000);
    }

    stopMetricsUpdate() {
        if (this.metricsInterval) {
            clearInterval(this.metricsInterval);
            this.metricsInterval = null;
        }
    }

    updateMetrics() {
        if (!this.collector || !this.isRecording) return;

        const interactions = this.collector.interactions;
        const dataSize = JSON.stringify(interactions).length;

        document.getElementById('interactionCount').textContent = interactions.length;
        document.getElementById('dataSize').textContent = UIUtils.formatFileSize(dataSize);
    }

    minimizePanel() {
        this.isMinimized = true;
        document.getElementById('floatingPanel').style.display = 'none';
        document.getElementById('minimizedPanel').style.display = 'flex';
    }

    restorePanel() {
        this.isMinimized = false;
        document.getElementById('floatingPanel').style.display = 'block';
        document.getElementById('minimizedPanel').style.display = 'none';
    }

    toggleMinimize() {
        if (this.isMinimized) {
            this.restorePanel();
        } else {
            this.minimizePanel();
        }
    }

    showSettings() {
        // Load current settings into form
        document.getElementById('sampleRateSlider').value = this.settings.sampleRate;
        document.getElementById('sampleRateValue').textContent = this.settings.sampleRate;
        document.getElementById('opacitySlider').value = this.settings.opacity;
        document.getElementById('opacityValue').textContent = this.settings.opacity + '%';
        document.getElementById('alwaysOnTopCheck').checked = this.settings.alwaysOnTop;
        document.getElementById('autoMinimizeCheck').checked = this.settings.autoMinimize;
        document.getElementById('captureVelocity').checked = this.settings.captureVelocity;
        document.getElementById('captureAcceleration').checked = this.settings.captureAcceleration;
        document.getElementById('captureScrollMomentum').checked = this.settings.captureScrollMomentum;

        document.getElementById('settingsModal').classList.add('show');
    }

    hideSettings() {
        document.getElementById('settingsModal').classList.remove('show');
    }

    saveSettings() {
        // Get values from form
        this.settings.sampleRate = parseInt(document.getElementById('sampleRateSlider').value);
        this.settings.opacity = parseInt(document.getElementById('opacitySlider').value);
        this.settings.alwaysOnTop = document.getElementById('alwaysOnTopCheck').checked;
        this.settings.autoMinimize = document.getElementById('autoMinimizeCheck').checked;
        this.settings.captureVelocity = document.getElementById('captureVelocity').checked;
        this.settings.captureAcceleration = document.getElementById('captureAcceleration').checked;
        this.settings.captureScrollMomentum = document.getElementById('captureScrollMomentum').checked;

        // Apply settings
        this.applySettings();

        // Save to storage
        UIUtils.saveUIState('floating-settings', this.settings);

        this.hideSettings();
        this.showToast('Settings saved', 'success');
    }

    resetSettings() {
        this.settings = {
            sampleRate: 60,
            opacity: 95,
            alwaysOnTop: true,
            autoMinimize: false,
            captureVelocity: true,
            captureAcceleration: true,
            captureScrollMomentum: true
        };

        this.applySettings();
        this.showSettings(); // Refresh the form
        this.showToast('Settings reset to defaults', 'info');
    }

    loadSettings() {
        const savedSettings = UIUtils.loadUIState('floating-settings');
        if (savedSettings) {
            this.settings = { ...this.settings, ...savedSettings };
            this.applySettings();
        }
    }

    applySettings() {
        this.updatePanelOpacity(this.settings.opacity);
        // Note: Always on top would require additional browser APIs or Electron
    }

    updatePanelOpacity(opacity) {
        const panel = document.getElementById('floatingPanel');
        const minimizedPanel = document.getElementById('minimizedPanel');
        const opacityValue = opacity / 100;

        panel.style.background = `rgba(255, 255, 255, ${opacityValue})`;
        minimizedPanel.style.background = `rgba(255, 255, 255, ${opacityValue})`;
    }

    showHistory() {
        const sessions = UIUtils.getSavedSessions();
        const historyList = document.getElementById('historyList');

        if (sessions.length === 0) {
            historyList.innerHTML = '<div class="empty-state">No recording history available.</div>';
        } else {
            historyList.innerHTML = sessions.map(session => `
                <div class="history-item">
                    <div class="history-info">
                        <div class="history-title">Session ${session.id.substring(0, 8)}</div>
                        <div class="history-meta">
                            ${UIUtils.formatTimestamp(session.timestamp)} •
                            ${UIUtils.formatDuration(session.duration)} •
                            ${session.interactionCount} interactions
                        </div>
                    </div>
                    <div class="history-actions">
                        <button class="btn btn-sm btn-primary" onclick="floatingPanel.exportSession('${session.id}')">
                            Export
                        </button>
                        <button class="btn btn-sm btn-error" onclick="floatingPanel.deleteSession('${session.id}')">
                            Delete
                        </button>
                    </div>
                </div>
            `).join('');
        }

        document.getElementById('historyModal').classList.add('show');
    }

    hideHistory() {
        document.getElementById('historyModal').classList.remove('show');
    }

    exportSession(sessionId) {
        const sessionData = UIUtils.loadSession(sessionId);
        if (!sessionData) {
            this.showToast('Session not found', 'error');
            return;
        }

        const filename = UIUtils.generateSessionFilename(sessionData.session.id, 'json');
        UIUtils.downloadFile(JSON.stringify(sessionData, null, 2), filename);
        this.showToast('Session exported', 'success');
    }

    deleteSession(sessionId) {
        if (confirm('Delete this session? This cannot be undone.')) {
            UIUtils.deleteSession(sessionId);
            this.showHistory(); // Refresh the list
            this.showToast('Session deleted', 'success');
        }
    }

    clearHistory() {
        if (confirm('Clear all recording history? This cannot be undone.')) {
            const sessions = UIUtils.getSavedSessions();
            sessions.forEach(session => UIUtils.deleteSession(session.id));
            this.showHistory(); // Refresh the list
            this.showToast('History cleared', 'success');
        }
    }

    exportAllSessions() {
        const sessions = UIUtils.getSavedSessions();
        if (sessions.length === 0) {
            this.showToast('No sessions to export', 'warning');
            return;
        }

        const allSessionsData = sessions.map(session => UIUtils.loadSession(session.id)).filter(Boolean);
        const exportData = {
            exportTimestamp: new Date().toISOString(),
            totalSessions: allSessionsData.length,
            sessions: allSessionsData
        };

        const filename = `all-sessions-${new Date().toISOString().split('T')[0]}.json`;
        UIUtils.downloadFile(JSON.stringify(exportData, null, 2), filename);
        this.showToast(`Exported ${allSessionsData.length} sessions`, 'success');
    }

    exportLastSession() {
        if (this.currentSession) {
            const filename = UIUtils.generateSessionFilename(this.currentSession.session.id, 'json');
            UIUtils.downloadFile(JSON.stringify(this.currentSession, null, 2), filename);
            this.showToast('Last session exported', 'success');
        } else {
            // Try to get the most recent session
            const sessions = UIUtils.getSavedSessions();
            if (sessions.length > 0) {
                this.exportSession(sessions[0].id);
            } else {
                this.showToast('No session to export', 'warning');
            }
        }
    }

    showHelp() {
        document.getElementById('helpModal').classList.add('show');
    }

    hideHelp() {
        document.getElementById('helpModal').classList.remove('show');
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;

        const container = document.getElementById('toastContainer');
        container.appendChild(toast);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.style.animation = 'toastSlideOut 0.3s ease forwards';
                setTimeout(() => {
                    if (toast.parentNode) {
                        container.removeChild(toast);
                    }
                }, 300);
            }
        }, 3000);
    }
}

// Add toast slide out animation
const style = document.createElement('style');
style.textContent = `
    @keyframes toastSlideOut {
        from {
            opacity: 1;
            transform: translateY(0);
        }
        to {
            opacity: 0;
            transform: translateY(-20px);
        }
    }

    .history-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        border: 1px solid #eee;
        border-radius: 8px;
        margin-bottom: 8px;
        background: #fafafa;
    }

    .history-info {
        flex: 1;
    }

    .history-title {
        font-weight: 600;
        margin-bottom: 4px;
    }

    .history-meta {
        font-size: 12px;
        color: #666;
    }

    .history-actions {
        display: flex;
        gap: 8px;
    }

    .empty-state {
        text-align: center;
        padding: 40px 20px;
        color: #999;
        font-style: italic;
    }
`;
document.head.appendChild(style);

// Initialize floating panel when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.floatingPanel = new FloatingPanelController();
});

/**
 * Dashboard Control Center Styles
 */

.dashboard-container {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--background-color) 0%, var(--surface-color) 100%);
}

/* Header */
.dashboard-header {
    background-color: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dashboard-title {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 12px;
}

.title-icon {
    font-size: 28px;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

.status-display {
    display: flex;
    align-items: center;
}

.theme-selector {
    min-width: 150px;
}

/* Main Dashboard */
.dashboard-main {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto auto;
    gap: 24px;
    grid-template-areas: 
        "controls metrics"
        "sessions sessions"
        "visualization visualization";
}

.control-panel {
    grid-area: controls;
}

.metrics-section {
    grid-area: metrics;
}

.session-management {
    grid-area: sessions;
}

.visualization-section {
    grid-area: visualization;
}

/* Control Panel */
.control-buttons {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
    flex-wrap: wrap;
}

.control-buttons .btn {
    flex: 1;
    min-width: 140px;
}

.btn-icon {
    font-size: 16px;
}

.recording-settings {
    border-top: 1px solid var(--border-color);
    padding-top: 24px;
}

.settings-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 16px;
    margin-top: 16px;
}

/* Metrics */
.recording-timer {
    font-family: 'Courier New', monospace;
    font-size: 18px;
    font-weight: bold;
    color: var(--primary-color);
    background-color: var(--background-color);
    padding: 8px 16px;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.metrics-grid {
    margin-bottom: 24px;
}

.metric-card {
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.interaction-rate-chart {
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 16px;
}

/* Session Management */
.header-actions {
    display: flex;
    gap: 8px;
}

.session-list {
    max-height: 400px;
    overflow-y: auto;
}

.session-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 12px;
    background-color: var(--background-color);
    transition: var(--transition);
}

.session-item:hover {
    background-color: var(--surface-color);
    transform: translateX(4px);
}

.session-info {
    flex: 1;
}

.session-title {
    font-weight: 600;
    margin-bottom: 4px;
}

.session-meta {
    font-size: 12px;
    color: var(--text-muted);
    display: flex;
    gap: 16px;
}

.session-actions {
    display: flex;
    gap: 8px;
}

.empty-state {
    text-align: center;
    padding: 48px 24px;
    color: var(--text-muted);
}

/* Visualization */
.visualization-controls {
    display: flex;
    gap: 12px;
}

.visualization-container {
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 16px;
    text-align: center;
}

#visualizationCanvas {
    max-width: 100%;
    height: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-muted);
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.modal-close:hover {
    background-color: var(--border-color);
    color: var(--text-color);
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    padding: 16px 24px 24px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-main {
        grid-template-columns: 1fr;
        grid-template-areas: 
            "controls"
            "metrics"
            "sessions"
            "visualization";
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }
    
    .dashboard-main {
        padding: 16px;
        gap: 16px;
    }
    
    .control-buttons {
        flex-direction: column;
    }
    
    .control-buttons .btn {
        min-width: auto;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .session-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .session-actions {
        align-self: stretch;
        justify-content: space-between;
    }
}

/* Animation for recording state */
@keyframes recording-pulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
    }
}

.recording-active .control-panel {
    animation: recording-pulse 2s infinite;
    border-radius: var(--border-radius);
}

/* Data quality indicators */
.quality-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-left: 8px;
}

.quality-excellent {
    background-color: var(--success-color);
}

.quality-good {
    background-color: var(--warning-color);
}

.quality-poor {
    background-color: var(--error-color);
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Timeline Recorder - Human Interaction Recorder</title>
    <link rel="stylesheet" href="../shared/styles.css">
    <link rel="stylesheet" href="timeline.css">
</head>
<body>
    <div class="timeline-container">
        <!-- Header -->
        <header class="timeline-header">
            <div class="header-left">
                <h1 class="app-title">
                    <span class="title-icon">⏱️</span>
                    Timeline Recorder
                </h1>
                <div class="recording-status" id="recordingStatus">
                    <div class="status-dot"></div>
                    <span class="status-text">Ready</span>
                </div>
            </div>
            
            <div class="header-center">
                <div class="timeline-controls">
                    <button class="timeline-btn record-btn" id="recordBtn" title="Start Recording">
                        <span>🔴</span>
                    </button>
                    <button class="timeline-btn pause-btn" id="pauseBtn" title="Pause" disabled>
                        <span>⏸️</span>
                    </button>
                    <button class="timeline-btn stop-btn" id="stopBtn" title="Stop" disabled>
                        <span>⏹️</span>
                    </button>
                    <div class="timeline-divider"></div>
                    <button class="timeline-btn play-btn" id="playBtn" title="Play Session" disabled>
                        <span>▶️</span>
                    </button>
                    <button class="timeline-btn rewind-btn" id="rewindBtn" title="Rewind" disabled>
                        <span>⏪</span>
                    </button>
                </div>
            </div>
            
            <div class="header-right">
                <div class="session-info">
                    <div class="current-time" id="currentTime">00:00:00</div>
                    <div class="total-interactions" id="totalInteractions">0 interactions</div>
                </div>
            </div>
        </header>

        <!-- Main Timeline Area -->
        <main class="timeline-main">
            <!-- Timeline Ruler -->
            <div class="timeline-ruler" id="timelineRuler">
                <div class="ruler-track">
                    <div class="playhead" id="playhead"></div>
                    <div class="ruler-markers" id="rulerMarkers"></div>
                </div>
            </div>

            <!-- Timeline Tracks -->
            <div class="timeline-tracks" id="timelineTracks">
                <!-- Mouse Movement Track -->
                <div class="track mouse-track">
                    <div class="track-header">
                        <div class="track-title">
                            <span class="track-icon">🖱️</span>
                            Mouse Movement
                        </div>
                        <div class="track-controls">
                            <button class="track-toggle" data-track="mouse" title="Toggle visibility">👁️</button>
                            <button class="track-solo" data-track="mouse" title="Solo track">🎯</button>
                        </div>
                    </div>
                    <div class="track-content mouse-content" id="mouseTrack">
                        <canvas class="track-canvas" id="mouseCanvas"></canvas>
                    </div>
                </div>

                <!-- Click Events Track -->
                <div class="track click-track">
                    <div class="track-header">
                        <div class="track-title">
                            <span class="track-icon">👆</span>
                            Click Events
                        </div>
                        <div class="track-controls">
                            <button class="track-toggle" data-track="click" title="Toggle visibility">👁️</button>
                            <button class="track-solo" data-track="click" title="Solo track">🎯</button>
                        </div>
                    </div>
                    <div class="track-content click-content" id="clickTrack">
                        <div class="event-markers" id="clickMarkers"></div>
                    </div>
                </div>

                <!-- Keyboard Events Track -->
                <div class="track keyboard-track">
                    <div class="track-header">
                        <div class="track-title">
                            <span class="track-icon">⌨️</span>
                            Keyboard Events
                        </div>
                        <div class="track-controls">
                            <button class="track-toggle" data-track="keyboard" title="Toggle visibility">👁️</button>
                            <button class="track-solo" data-track="keyboard" title="Solo track">🎯</button>
                        </div>
                    </div>
                    <div class="track-content keyboard-content" id="keyboardTrack">
                        <div class="event-markers" id="keyboardMarkers"></div>
                    </div>
                </div>

                <!-- Scroll Events Track -->
                <div class="track scroll-track">
                    <div class="track-header">
                        <div class="track-title">
                            <span class="track-icon">📜</span>
                            Scroll Events
                        </div>
                        <div class="track-controls">
                            <button class="track-toggle" data-track="scroll" title="Toggle visibility">👁️</button>
                            <button class="track-solo" data-track="scroll" title="Solo track">🎯</button>
                        </div>
                    </div>
                    <div class="track-content scroll-content" id="scrollTrack">
                        <canvas class="track-canvas" id="scrollCanvas"></canvas>
                    </div>
                </div>
            </div>
        </main>

        <!-- Bottom Panel -->
        <div class="bottom-panel">
            <!-- Session List -->
            <div class="session-panel">
                <div class="panel-header">
                    <h3>Recording Sessions</h3>
                    <div class="panel-controls">
                        <button class="btn btn-sm btn-secondary" id="refreshSessions">Refresh</button>
                        <button class="btn btn-sm btn-primary" id="newSession">New Session</button>
                    </div>
                </div>
                <div class="session-list" id="sessionList">
                    <div class="empty-state">
                        <p>No sessions recorded yet. Click "New Session" to start.</p>
                    </div>
                </div>
            </div>

            <!-- Properties Panel -->
            <div class="properties-panel">
                <div class="panel-header">
                    <h3>Event Properties</h3>
                </div>
                <div class="properties-content" id="propertiesContent">
                    <div class="no-selection">
                        <p>Select an event on the timeline to view its properties.</p>
                    </div>
                </div>
            </div>

            <!-- Analysis Panel -->
            <div class="analysis-panel">
                <div class="panel-header">
                    <h3>Session Analysis</h3>
                </div>
                <div class="analysis-content" id="analysisContent">
                    <div class="analysis-metrics">
                        <div class="metric-item">
                            <span class="metric-label">Duration</span>
                            <span class="metric-value" id="sessionDuration">--</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">Mouse Distance</span>
                            <span class="metric-value" id="mouseDistance">--</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">Avg Speed</span>
                            <span class="metric-value" id="avgSpeed">--</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">Click Rate</span>
                            <span class="metric-value" id="clickRate">--</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Export Modal -->
        <div class="modal-overlay" id="exportModal">
            <div class="export-modal">
                <div class="modal-header">
                    <h3>Export Timeline Session</h3>
                    <button class="close-btn" id="closeExportModal">&times;</button>
                </div>
                <div class="modal-content">
                    <div class="export-options">
                        <div class="option-group">
                            <label class="option-label">Export Format</label>
                            <select class="form-control" id="exportFormat">
                                <option value="json">JSON (Complete Data)</option>
                                <option value="csv">CSV (Simplified)</option>
                                <option value="timeline">Timeline Project File</option>
                            </select>
                        </div>
                        
                        <div class="option-group">
                            <label class="option-label">Time Range</label>
                            <div class="time-range-controls">
                                <input type="number" class="form-control time-input" id="startTime" placeholder="Start (ms)" min="0">
                                <span class="range-separator">to</span>
                                <input type="number" class="form-control time-input" id="endTime" placeholder="End (ms)" min="0">
                            </div>
                        </div>
                        
                        <div class="option-group">
                            <label class="option-label">Include Tracks</label>
                            <div class="track-checkboxes">
                                <label><input type="checkbox" id="includeMouse" checked> Mouse Movement</label>
                                <label><input type="checkbox" id="includeClick" checked> Click Events</label>
                                <label><input type="checkbox" id="includeKeyboard" checked> Keyboard Events</label>
                                <label><input type="checkbox" id="includeScroll" checked> Scroll Events</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="cancelExport">Cancel</button>
                    <button class="btn btn-primary" id="confirmExport">Export</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../shared/data-collector.js"></script>
    <script src="../shared/utils.js"></script>
    <script src="timeline.js"></script>
</body>
</html>

/**
 * Dashboard Control Center JavaScript
 * Main application logic for the dashboard interface
 */

class DashboardController {
    constructor() {
        this.collector = null;
        this.isRecording = false;
        this.isPaused = false;
        this.recordingStartTime = null;
        this.timerInterval = null;
        this.metricsInterval = null;
        this.currentSession = null;
        
        // Chart context
        this.chartCanvas = null;
        this.chartContext = null;
        this.interactionHistory = [];
        
        // Visualization
        this.visualizationCanvas = null;
        this.visualizationContext = null;
        this.mouseTrail = [];
        
        this.initializeUI();
        this.bindEvents();
        this.loadSavedSessions();
        this.setupVisualization();
    }
    
    initializeUI() {
        // Load saved theme
        const savedTheme = UIUtils.loadSavedTheme();
        document.getElementById('themeSelector').value = savedTheme;
        
        // Initialize chart canvas
        this.chartCanvas = document.getElementById('interactionChart');
        this.chartContext = this.chartCanvas.getContext('2d');
        this.setupChart();
        
        // Initialize visualization canvas
        this.visualizationCanvas = document.getElementById('visualizationCanvas');
        this.visualizationContext = this.visualizationCanvas.getContext('2d');
        
        // Load saved settings
        const savedSettings = UIUtils.loadUIState('dashboard');
        if (savedSettings.targetUrl) {
            document.getElementById('targetUrl').value = savedSettings.targetUrl;
        }
        if (savedSettings.sampleRate) {
            document.getElementById('sampleRate').value = savedSettings.sampleRate;
        }
    }
    
    bindEvents() {
        // Recording controls
        document.getElementById('startRecording').addEventListener('click', () => this.startRecording());
        document.getElementById('pauseRecording').addEventListener('click', () => this.pauseRecording());
        document.getElementById('stopRecording').addEventListener('click', () => this.stopRecording());
        
        // Theme selector
        document.getElementById('themeSelector').addEventListener('change', (e) => {
            UIUtils.applyTheme(e.target.value);
        });
        
        // Session management
        document.getElementById('refreshSessions').addEventListener('click', () => this.loadSavedSessions());
        document.getElementById('exportAll').addEventListener('click', () => this.exportAllSessions());
        
        // Visualization controls
        document.getElementById('visualizationType').addEventListener('change', (e) => {
            this.updateVisualization(e.target.value);
        });
        
        // Modal controls
        document.getElementById('closeExportModal').addEventListener('click', () => this.closeExportModal());
        document.getElementById('cancelExport').addEventListener('click', () => this.closeExportModal());
        document.getElementById('confirmExport').addEventListener('click', () => this.performExport());
        
        // Settings auto-save
        ['targetUrl', 'sampleRate', 'captureVelocity', 'captureAcceleration'].forEach(id => {
            document.getElementById(id).addEventListener('change', () => this.saveSettings());
        });
        
        // Keyboard shortcuts
        UIUtils.setupKeyboardShortcuts({
            'ctrl+r': () => this.toggleRecording(),
            'ctrl+s': () => this.stopRecording(),
            'ctrl+e': () => this.exportCurrentSession(),
            'escape': () => this.closeExportModal()
        });
    }
    
    saveSettings() {
        const settings = {
            targetUrl: document.getElementById('targetUrl').value,
            sampleRate: parseInt(document.getElementById('sampleRate').value),
            captureVelocity: document.getElementById('captureVelocity').checked,
            captureAcceleration: document.getElementById('captureAcceleration').checked
        };
        UIUtils.saveUIState('dashboard', settings);
    }
    
    startRecording() {
        if (this.isRecording) return;
        
        const settings = {
            sampleRate: parseInt(document.getElementById('sampleRate').value),
            captureVelocity: document.getElementById('captureVelocity').checked,
            captureAcceleration: document.getElementById('captureAcceleration').checked,
            onStart: (sessionId) => this.onRecordingStart(sessionId),
            onStop: (sessionData) => this.onRecordingStop(sessionData),
            onDataPoint: (interaction) => this.onDataPoint(interaction),
            onError: (error) => this.onError(error)
        };
        
        this.collector = new HumanInteractionCollector(settings);
        
        if (this.collector.startRecording()) {
            this.isRecording = true;
            this.isPaused = false;
            this.recordingStartTime = Date.now();
            this.updateUI();
            this.startTimer();
            this.startMetricsUpdate();
            
            UIUtils.showNotification('Recording started successfully!', 'success');
        } else {
            UIUtils.showNotification('Failed to start recording', 'error');
        }
    }
    
    pauseRecording() {
        if (!this.isRecording || this.isPaused) return;
        
        this.isPaused = true;
        // Note: Actual pause functionality would need to be implemented in the collector
        this.updateUI();
        UIUtils.showNotification('Recording paused', 'warning');
    }
    
    stopRecording() {
        if (!this.isRecording) return;
        
        const sessionData = this.collector.stopRecording();
        this.isRecording = false;
        this.isPaused = false;
        this.currentSession = sessionData;
        
        this.stopTimer();
        this.stopMetricsUpdate();
        this.updateUI();
        
        if (sessionData) {
            // Add session description
            const description = document.getElementById('sessionDescription').value;
            if (description) {
                sessionData.session.description = description;
            }
            
            // Save session
            UIUtils.saveSession(sessionData);
            this.loadSavedSessions();
            
            UIUtils.showNotification(`Recording stopped. Captured ${sessionData.interactions.length} interactions.`, 'success');
            
            // Show export modal
            this.showExportModal(sessionData);
        }
    }
    
    toggleRecording() {
        if (this.isRecording) {
            this.stopRecording();
        } else {
            this.startRecording();
        }
    }
    
    onRecordingStart(sessionId) {
        console.log('Recording started:', sessionId);
        this.mouseTrail = [];
        this.interactionHistory = [];
    }
    
    onRecordingStop(sessionData) {
        console.log('Recording stopped:', sessionData);
    }
    
    onDataPoint(interaction) {
        // Update real-time visualization
        if (interaction.type === 'mouse_move') {
            this.mouseTrail.push({ x: interaction.x, y: interaction.y, timestamp: interaction.timestamp });
            // Keep only last 100 points for performance
            if (this.mouseTrail.length > 100) {
                this.mouseTrail.shift();
            }
        }
        
        // Update interaction history for chart
        const now = Date.now();
        this.interactionHistory.push({ timestamp: now, type: interaction.type });
        
        // Keep only last 60 seconds of data
        const cutoff = now - 60000;
        this.interactionHistory = this.interactionHistory.filter(item => item.timestamp > cutoff);
        
        this.updateChart();
        this.updateVisualization();
    }
    
    onError(error) {
        console.error('Recording error:', error);
        UIUtils.showNotification('Recording error: ' + error.message, 'error');
    }
    
    updateUI() {
        const startBtn = document.getElementById('startRecording');
        const pauseBtn = document.getElementById('pauseRecording');
        const stopBtn = document.getElementById('stopRecording');
        const statusIndicator = document.getElementById('recordingStatus');
        
        if (this.isRecording) {
            startBtn.disabled = true;
            pauseBtn.disabled = this.isPaused;
            stopBtn.disabled = false;
            
            if (this.isPaused) {
                statusIndicator.className = 'status-indicator status-processing';
                statusIndicator.textContent = 'Paused';
                document.body.classList.remove('recording-active');
            } else {
                statusIndicator.className = 'status-indicator status-recording';
                statusIndicator.textContent = 'Recording';
                document.body.classList.add('recording-active');
            }
        } else {
            startBtn.disabled = false;
            pauseBtn.disabled = true;
            stopBtn.disabled = true;
            statusIndicator.className = 'status-indicator status-idle';
            statusIndicator.textContent = 'Idle';
            document.body.classList.remove('recording-active');
        }
    }
    
    startTimer() {
        this.timerInterval = setInterval(() => {
            if (!this.isPaused) {
                const elapsed = Date.now() - this.recordingStartTime;
                const formatted = this.formatTime(elapsed);
                document.getElementById('recordingTimer').textContent = formatted;
            }
        }, 1000);
    }
    
    stopTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
        document.getElementById('recordingTimer').textContent = '00:00:00';
    }
    
    formatTime(milliseconds) {
        const totalSeconds = Math.floor(milliseconds / 1000);
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;
        
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    
    startMetricsUpdate() {
        this.metricsInterval = setInterval(() => {
            this.updateMetrics();
        }, 1000);
    }
    
    stopMetricsUpdate() {
        if (this.metricsInterval) {
            clearInterval(this.metricsInterval);
            this.metricsInterval = null;
        }
    }
    
    updateMetrics() {
        if (!this.collector || !this.isRecording) return;

        const interactions = this.collector.interactions;
        const typeCounts = this.collector.getInteractionTypeCounts();

        document.getElementById('totalInteractions').textContent = interactions.length;
        document.getElementById('mouseMovements').textContent = typeCounts.mouse_move || 0;
        document.getElementById('mouseClicks').textContent = typeCounts.mouse_click || 0;
        document.getElementById('keystrokes').textContent = (typeCounts.key_down || 0) + (typeCounts.key_up || 0);
        document.getElementById('scrollEvents').textContent = typeCounts.scroll || 0;

        const dataSize = JSON.stringify(interactions).length;
        document.getElementById('dataSize').textContent = UIUtils.formatFileSize(dataSize);
    }

    setupChart() {
        this.chartContext.fillStyle = getComputedStyle(document.documentElement).getPropertyValue('--surface-color');
        this.chartContext.fillRect(0, 0, this.chartCanvas.width, this.chartCanvas.height);

        // Draw grid
        this.chartContext.strokeStyle = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
        this.chartContext.lineWidth = 1;

        for (let i = 0; i <= 10; i++) {
            const x = (this.chartCanvas.width / 10) * i;
            this.chartContext.beginPath();
            this.chartContext.moveTo(x, 0);
            this.chartContext.lineTo(x, this.chartCanvas.height);
            this.chartContext.stroke();
        }

        for (let i = 0; i <= 5; i++) {
            const y = (this.chartCanvas.height / 5) * i;
            this.chartContext.beginPath();
            this.chartContext.moveTo(0, y);
            this.chartContext.lineTo(this.chartCanvas.width, y);
            this.chartContext.stroke();
        }
    }

    updateChart() {
        if (!this.chartContext) return;

        // Clear canvas
        this.chartContext.clearRect(0, 0, this.chartCanvas.width, this.chartCanvas.height);
        this.setupChart();

        if (this.interactionHistory.length === 0) return;

        // Group interactions by second
        const now = Date.now();
        const buckets = {};

        this.interactionHistory.forEach(item => {
            const secondBucket = Math.floor((now - item.timestamp) / 1000);
            if (secondBucket < 60) {
                buckets[secondBucket] = (buckets[secondBucket] || 0) + 1;
            }
        });

        // Draw bars
        const maxCount = Math.max(...Object.values(buckets), 1);
        const barWidth = this.chartCanvas.width / 60;

        this.chartContext.fillStyle = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');

        for (let i = 0; i < 60; i++) {
            const count = buckets[59 - i] || 0;
            const barHeight = (count / maxCount) * (this.chartCanvas.height - 20);
            const x = i * barWidth;
            const y = this.chartCanvas.height - barHeight;

            this.chartContext.fillRect(x, y, barWidth - 1, barHeight);
        }
    }

    setupVisualization() {
        this.updateVisualization('heatmap');
    }

    updateVisualization(type = null) {
        if (!this.visualizationContext) return;

        const visualizationType = type || document.getElementById('visualizationType').value;

        // Clear canvas
        this.visualizationContext.clearRect(0, 0, this.visualizationCanvas.width, this.visualizationCanvas.height);

        // Set background
        this.visualizationContext.fillStyle = getComputedStyle(document.documentElement).getPropertyValue('--background-color');
        this.visualizationContext.fillRect(0, 0, this.visualizationCanvas.width, this.visualizationCanvas.height);

        switch (visualizationType) {
            case 'heatmap':
                this.drawHeatmap();
                break;
            case 'path':
                this.drawMousePath();
                break;
            case 'velocity':
                this.drawVelocityGraph();
                break;
            case 'timing':
                this.drawTimingAnalysis();
                break;
        }
    }

    drawHeatmap() {
        if (this.mouseTrail.length === 0) {
            this.visualizationContext.fillStyle = getComputedStyle(document.documentElement).getPropertyValue('--text-muted');
            this.visualizationContext.font = '16px sans-serif';
            this.visualizationContext.textAlign = 'center';
            this.visualizationContext.fillText('Mouse heatmap will appear here during recording',
                this.visualizationCanvas.width / 2, this.visualizationCanvas.height / 2);
            return;
        }

        // Create heatmap from mouse trail
        const scaleX = this.visualizationCanvas.width / window.innerWidth;
        const scaleY = this.visualizationCanvas.height / window.innerHeight;

        this.mouseTrail.forEach((point, index) => {
            const x = point.x * scaleX;
            const y = point.y * scaleY;
            const alpha = Math.max(0.1, index / this.mouseTrail.length);

            const gradient = this.visualizationContext.createRadialGradient(x, y, 0, x, y, 20);
            gradient.addColorStop(0, `rgba(255, 0, 0, ${alpha})`);
            gradient.addColorStop(1, 'rgba(255, 0, 0, 0)');

            this.visualizationContext.fillStyle = gradient;
            this.visualizationContext.fillRect(x - 20, y - 20, 40, 40);
        });
    }

    drawMousePath() {
        if (this.mouseTrail.length < 2) {
            this.visualizationContext.fillStyle = getComputedStyle(document.documentElement).getPropertyValue('--text-muted');
            this.visualizationContext.font = '16px sans-serif';
            this.visualizationContext.textAlign = 'center';
            this.visualizationContext.fillText('Mouse path will appear here during recording',
                this.visualizationCanvas.width / 2, this.visualizationCanvas.height / 2);
            return;
        }

        const scaleX = this.visualizationCanvas.width / window.innerWidth;
        const scaleY = this.visualizationCanvas.height / window.innerHeight;

        this.visualizationContext.strokeStyle = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
        this.visualizationContext.lineWidth = 2;
        this.visualizationContext.beginPath();

        this.mouseTrail.forEach((point, index) => {
            const x = point.x * scaleX;
            const y = point.y * scaleY;

            if (index === 0) {
                this.visualizationContext.moveTo(x, y);
            } else {
                this.visualizationContext.lineTo(x, y);
            }
        });

        this.visualizationContext.stroke();

        // Draw start and end points
        if (this.mouseTrail.length > 0) {
            const start = this.mouseTrail[0];
            const end = this.mouseTrail[this.mouseTrail.length - 1];

            // Start point (green)
            this.visualizationContext.fillStyle = getComputedStyle(document.documentElement).getPropertyValue('--success-color');
            this.visualizationContext.beginPath();
            this.visualizationContext.arc(start.x * scaleX, start.y * scaleY, 5, 0, 2 * Math.PI);
            this.visualizationContext.fill();

            // End point (red)
            this.visualizationContext.fillStyle = getComputedStyle(document.documentElement).getPropertyValue('--error-color');
            this.visualizationContext.beginPath();
            this.visualizationContext.arc(end.x * scaleX, end.y * scaleY, 5, 0, 2 * Math.PI);
            this.visualizationContext.fill();
        }
    }

    drawVelocityGraph() {
        this.visualizationContext.fillStyle = getComputedStyle(document.documentElement).getPropertyValue('--text-muted');
        this.visualizationContext.font = '16px sans-serif';
        this.visualizationContext.textAlign = 'center';
        this.visualizationContext.fillText('Velocity analysis will appear here during recording',
            this.visualizationCanvas.width / 2, this.visualizationCanvas.height / 2);
    }

    drawTimingAnalysis() {
        this.visualizationContext.fillStyle = getComputedStyle(document.documentElement).getPropertyValue('--text-muted');
        this.visualizationContext.font = '16px sans-serif';
        this.visualizationContext.textAlign = 'center';
        this.visualizationContext.fillText('Timing analysis will appear here during recording',
            this.visualizationCanvas.width / 2, this.visualizationCanvas.height / 2);
    }

    loadSavedSessions() {
        const sessions = UIUtils.getSavedSessions();
        const sessionList = document.getElementById('sessionList');

        if (sessions.length === 0) {
            sessionList.innerHTML = '<div class="empty-state"><p>No recorded sessions yet. Start recording to see sessions here.</p></div>';
            return;
        }

        sessionList.innerHTML = sessions.map(session => `
            <div class="session-item">
                <div class="session-info">
                    <div class="session-title">Session ${session.id.substring(0, 8)}</div>
                    <div class="session-meta">
                        <span>📅 ${UIUtils.formatTimestamp(session.timestamp)}</span>
                        <span>⏱️ ${UIUtils.formatDuration(session.duration)}</span>
                        <span>🔢 ${session.interactionCount} interactions</span>
                        <span>📊 ${UIUtils.formatFileSize(session.size)}</span>
                    </div>
                </div>
                <div class="session-actions">
                    <button class="btn btn-secondary btn-sm" onclick="dashboard.viewSession('${session.id}')">
                        <span class="btn-icon">👁️</span>
                        View
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="dashboard.exportSession('${session.id}')">
                        <span class="btn-icon">📤</span>
                        Export
                    </button>
                    <button class="btn btn-error btn-sm" onclick="dashboard.deleteSession('${session.id}')">
                        <span class="btn-icon">🗑️</span>
                        Delete
                    </button>
                </div>
            </div>
        `).join('');
    }

    viewSession(sessionId) {
        const sessionData = UIUtils.loadSession(sessionId);
        if (!sessionData) {
            UIUtils.showNotification('Session not found', 'error');
            return;
        }

        // Show session details in a modal or new window
        const details = `
            Session ID: ${sessionData.session.id}
            Timestamp: ${UIUtils.formatTimestamp(sessionData.session.timestamp)}
            Duration: ${UIUtils.formatDuration(sessionData.session.duration)}
            URL: ${sessionData.session.url}
            Interactions: ${sessionData.interactions.length}

            Interaction Types:
            ${Object.entries(sessionData.metadata.interactionTypes).map(([type, count]) => `- ${type}: ${count}`).join('\n')}
        `;

        alert(details); // In a real app, this would be a proper modal
    }

    exportSession(sessionId) {
        const sessionData = UIUtils.loadSession(sessionId);
        if (!sessionData) {
            UIUtils.showNotification('Session not found', 'error');
            return;
        }

        this.showExportModal(sessionData);
    }

    deleteSession(sessionId) {
        if (confirm('Are you sure you want to delete this session? This action cannot be undone.')) {
            UIUtils.deleteSession(sessionId);
            this.loadSavedSessions();
            UIUtils.showNotification('Session deleted successfully', 'success');
        }
    }

    exportAllSessions() {
        const sessions = UIUtils.getSavedSessions();
        if (sessions.length === 0) {
            UIUtils.showNotification('No sessions to export', 'warning');
            return;
        }

        const allSessionsData = sessions.map(session => UIUtils.loadSession(session.id)).filter(Boolean);
        const exportData = {
            exportTimestamp: new Date().toISOString(),
            totalSessions: allSessionsData.length,
            sessions: allSessionsData
        };

        const filename = `all-sessions-${new Date().toISOString().split('T')[0]}.json`;
        UIUtils.downloadFile(JSON.stringify(exportData, null, 2), filename);
        UIUtils.showNotification(`Exported ${allSessionsData.length} sessions`, 'success');
    }

    showExportModal(sessionData) {
        this.currentSession = sessionData;
        document.getElementById('exportModal').classList.add('show');
    }

    closeExportModal() {
        document.getElementById('exportModal').classList.remove('show');
        this.currentSession = null;
    }

    performExport() {
        if (!this.currentSession) return;

        const format = document.getElementById('exportFormat').value;
        const includeMetadata = document.getElementById('includeMetadata').checked;

        let exportData = this.currentSession;
        if (!includeMetadata) {
            exportData = {
                session: this.currentSession.session,
                interactions: this.currentSession.interactions
            };
        }

        const filename = UIUtils.generateSessionFilename(this.currentSession.session.id, format);

        if (format === 'json') {
            UIUtils.downloadFile(JSON.stringify(exportData, null, 2), filename);
        } else if (format === 'csv') {
            const csvData = this.convertToCSV(exportData);
            UIUtils.downloadFile(csvData, filename, 'text/csv');
        }

        UIUtils.showNotification('Session exported successfully', 'success');
        this.closeExportModal();
    }

    convertToCSV(sessionData) {
        const headers = ['timestamp', 'type', 'x', 'y', 'key', 'target_tag', 'target_id', 'velocity_x', 'velocity_y'];
        const rows = [headers.join(',')];

        sessionData.interactions.forEach(interaction => {
            const row = [
                interaction.timestamp,
                interaction.type,
                interaction.x || '',
                interaction.y || '',
                interaction.key || '',
                interaction.target?.tagName || '',
                interaction.target?.id || '',
                interaction.velocity?.x || '',
                interaction.velocity?.y || ''
            ];
            rows.push(row.join(','));
        });

        return rows.join('\n');
    }

    exportCurrentSession() {
        if (this.currentSession) {
            this.showExportModal(this.currentSession);
        } else {
            UIUtils.showNotification('No session to export', 'warning');
        }
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new DashboardController();
});

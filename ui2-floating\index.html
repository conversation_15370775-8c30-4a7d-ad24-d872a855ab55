<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Floating Panel - Human Interaction Recorder</title>
    <link rel="stylesheet" href="../shared/styles.css">
    <link rel="stylesheet" href="floating.css">
</head>
<body>
    <!-- Main Floating Panel -->
    <div class="floating-panel" id="floatingPanel">
        <!-- Drag Handle -->
        <div class="panel-header" id="panelHeader">
            <div class="panel-title">
                <span class="title-icon">🎯</span>
                <span class="title-text">Recorder</span>
            </div>
            <div class="panel-controls">
                <button class="panel-btn minimize-btn" id="minimizeBtn" title="Minimize">
                    <span>−</span>
                </button>
                <button class="panel-btn settings-btn" id="settingsBtn" title="Settings">
                    <span>⚙️</span>
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="panel-content" id="panelContent">
            <!-- Status Display -->
            <div class="status-section">
                <div class="status-indicator-large" id="statusIndicator">
                    <div class="status-dot"></div>
                    <div class="status-text">Ready</div>
                </div>
                <div class="recording-timer" id="recordingTimer">00:00</div>
            </div>

            <!-- Quick Controls -->
            <div class="quick-controls">
                <button class="control-btn record-btn" id="recordBtn" title="Start Recording (Ctrl+R)">
                    <span class="btn-icon">🔴</span>
                </button>
                <button class="control-btn pause-btn" id="pauseBtn" title="Pause Recording" disabled>
                    <span class="btn-icon">⏸️</span>
                </button>
                <button class="control-btn stop-btn" id="stopBtn" title="Stop Recording (Ctrl+S)" disabled>
                    <span class="btn-icon">⏹️</span>
                </button>
            </div>

            <!-- Quick Stats -->
            <div class="quick-stats">
                <div class="stat-item">
                    <span class="stat-value" id="interactionCount">0</span>
                    <span class="stat-label">Interactions</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value" id="dataSize">0KB</span>
                    <span class="stat-label">Data</span>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <button class="action-btn" id="exportBtn" title="Export Last Session">
                    <span>📤</span>
                </button>
                <button class="action-btn" id="historyBtn" title="View History">
                    <span>📋</span>
                </button>
                <button class="action-btn" id="helpBtn" title="Help & Shortcuts">
                    <span>❓</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Minimized State -->
    <div class="minimized-panel" id="minimizedPanel" style="display: none;">
        <div class="minimized-content">
            <div class="minimized-status" id="minimizedStatus">
                <div class="status-dot"></div>
            </div>
            <button class="restore-btn" id="restoreBtn" title="Restore Panel">
                <span>🎯</span>
            </button>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal-overlay" id="settingsModal">
        <div class="settings-modal">
            <div class="modal-header">
                <h3>Recording Settings</h3>
                <button class="close-btn" id="closeSettingsBtn">&times;</button>
            </div>
            <div class="modal-content">
                <div class="setting-group">
                    <label class="setting-label">Sample Rate (Hz)</label>
                    <input type="range" class="setting-slider" id="sampleRateSlider" 
                           min="10" max="120" value="60">
                    <span class="setting-value" id="sampleRateValue">60</span>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Panel Opacity</label>
                    <input type="range" class="setting-slider" id="opacitySlider" 
                           min="50" max="100" value="95">
                    <span class="setting-value" id="opacityValue">95%</span>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Always on Top</label>
                    <input type="checkbox" class="setting-checkbox" id="alwaysOnTopCheck" checked>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Auto-minimize on Record</label>
                    <input type="checkbox" class="setting-checkbox" id="autoMinimizeCheck">
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Capture Options</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" id="captureVelocity" checked> Velocity</label>
                        <label><input type="checkbox" id="captureAcceleration" checked> Acceleration</label>
                        <label><input type="checkbox" id="captureScrollMomentum" checked> Scroll Momentum</label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="resetSettingsBtn">Reset</button>
                <button class="btn btn-primary" id="saveSettingsBtn">Save</button>
            </div>
        </div>
    </div>

    <!-- History Modal -->
    <div class="modal-overlay" id="historyModal">
        <div class="history-modal">
            <div class="modal-header">
                <h3>Recording History</h3>
                <button class="close-btn" id="closeHistoryBtn">&times;</button>
            </div>
            <div class="modal-content">
                <div class="history-list" id="historyList">
                    <!-- History items will be populated here -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="clearHistoryBtn">Clear All</button>
                <button class="btn btn-primary" id="exportAllBtn">Export All</button>
            </div>
        </div>
    </div>

    <!-- Help Modal -->
    <div class="modal-overlay" id="helpModal">
        <div class="help-modal">
            <div class="modal-header">
                <h3>Help & Keyboard Shortcuts</h3>
                <button class="close-btn" id="closeHelpBtn">&times;</button>
            </div>
            <div class="modal-content">
                <div class="help-section">
                    <h4>Keyboard Shortcuts</h4>
                    <div class="shortcut-list">
                        <div class="shortcut-item">
                            <kbd>Ctrl + R</kbd>
                            <span>Start/Stop Recording</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl + P</kbd>
                            <span>Pause/Resume Recording</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl + E</kbd>
                            <span>Export Last Session</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl + M</kbd>
                            <span>Minimize/Restore Panel</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl + ,</kbd>
                            <span>Open Settings</span>
                        </div>
                    </div>
                </div>
                
                <div class="help-section">
                    <h4>Usage Tips</h4>
                    <ul class="tip-list">
                        <li>Drag the panel by its header to reposition</li>
                        <li>Panel stays on top of other windows for easy access</li>
                        <li>Minimize to reduce visual distraction during recording</li>
                        <li>All interactions are automatically saved locally</li>
                        <li>Export data in JSON or CSV format for ML training</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- Scripts -->
    <script src="../shared/data-collector.js"></script>
    <script src="../shared/utils.js"></script>
    <script src="floating.js"></script>
</body>
</html>
